# Векторная база данных

## Обзор

Векторная база данных в Clojure Agent System предназначена для семантического поиска и анализа сходства между агентами. Она позволяет находить агентов на основе текстового описания их функций и возможностей.

## Архитектура

### Компоненты

1. **IVectorDatabase Protocol** - интерфейс для работы с векторной БД
2. **InMemoryVectorDB** - реализация в памяти
3. **Vector Operations** - операции с векторами (косинусное сходство)
4. **Text Embeddings** - преобразование текста в векторы

### Структура данных

```clojure
{:vectors {
  "agent-001" [0.1 0.2 0.3 ... 0.9]  ; вектор агента
  "agent-002" [0.2 0.1 0.4 ... 0.8]
}
 :metadata {
  "agent-001" {:type "worker" :description "Data processor"}
  "agent-002" {:type "manager" :description "Task coordinator"}
}}
```

## Протокол IVectorDatabase

```clojure
(defprotocol IVectorDatabase
  "Протокол для работы с векторной базой данных"
  
  (add-vector [this key vector metadata]
    "Добавляет вектор с метаданными")
  
  (get-vector [this key]
    "Получает вектор по ключу")
  
  (remove-vector [this key]
    "Удаляет вектор по ключу")
  
  (search-similar [this query-vector k]
    "Ищет k наиболее похожих векторов")
  
  (get-stats [this]
    "Возвращает статистику базы данных"))
```

## Использование

### Создание и настройка

```clojure
(require '[clojure-agent.db.vectordb :as vdb])

;; Создание экземпляра векторной БД
(def vector-db (vdb/create-in-memory-vectordb))

;; Запуск компонента
(def started-db (component/start vector-db))
```

### Добавление векторов

```clojure
;; Добавление вектора агента
(vdb/add-vector started-db 
                "agent-001" 
                [0.1 0.2 0.3 0.4 0.5]
                {:type "worker" 
                 :description "Data processing agent"
                 :capabilities ["data-transform" "validation"]})

;; Добавление через текстовое описание
(vdb/add-agent-embedding started-db
                        "agent-002"
                        "Task coordination and workflow management agent"
                        {:type "manager"})
```

### Поиск похожих векторов

```clojure
;; Поиск по вектору
(def query-vector [0.15 0.25 0.35 0.45 0.55])
(def results (vdb/search-similar started-db query-vector 5))

;; Поиск по тексту
(def text-results (vdb/search-by-text started-db 
                                     "data processing worker" 
                                     3))

;; Результат:
;; [{:key "agent-001" 
;;   :similarity 0.95
;;   :metadata {:type "worker" :description "..."}}
;;  {:key "agent-003"
;;   :similarity 0.87
;;   :metadata {:type "processor" :description "..."}}]
```

### Получение статистики

```clojure
(def stats (vdb/get-stats started-db))
;; {:total-vectors 10
;;  :average-vector-size 100.0
;;  :memory-usage "2.5 MB"}
```

## Алгоритмы

### Косинусное сходство

Для вычисления сходства между векторами используется косинусное сходство:

```clojure
(defn cosine-similarity [v1 v2]
  "Вычисляет косинусное сходство между двумя векторами"
  (let [dot-product (reduce + (map * v1 v2))
        magnitude-v1 (Math/sqrt (reduce + (map #(* % %) v1)))
        magnitude-v2 (Math/sqrt (reduce + (map #(* % %) v2)))]
    (if (or (zero? magnitude-v1) (zero? magnitude-v2))
      0.0
      (/ dot-product (* magnitude-v1 magnitude-v2)))))
```

### Текстовые эмбеддинги

Простая реализация преобразования текста в векторы:

```clojure
(defn text-to-vector [text]
  "Преобразует текст в вектор фиксированной длины"
  (let [words (-> text
                  clojure.string/lower-case
                  (clojure.string/split #"\s+"))
        word-counts (frequencies words)
        vocabulary (keys word-counts)
        vector-size 100]
    (mapv (fn [i]
            (let [word (nth vocabulary i nil)]
              (if word
                (/ (get word-counts word 0) (count words))
                0.0)))
          (range vector-size))))
```

## GraphQL интеграция

### Схема

```graphql
type VectorSearchResult {
  key: String!
  similarity: Float
  metadata: JSON
}

type VectorStats {
  total_vectors: Int
  average_vector_size: Float
  memory_usage: String
}

type Query {
  vectorSearch(query: String!, k: Int): [VectorSearchResult]
  vectorStats: VectorStats
}
```

### Резолверы

```clojure
(defn vector-search-resolver [context args _]
  (let [vectordb (:vectordb context)
        query (:query args)
        k (or (:k args) 10)]
    (vdb/search-by-text vectordb query k)))

(defn vector-stats-resolver [context _ _]
  (let [vectordb (:vectordb context)]
    (vdb/get-stats vectordb)))
```

## Оптимизация производительности

### Индексирование

Для больших объемов данных можно использовать приближенный поиск:

```clojure
(defn build-index [vectors]
  "Строит индекс для быстрого поиска"
  ;; Реализация LSH (Locality Sensitive Hashing)
  ;; или другого алгоритма приближенного поиска
  )
```

### Кэширование

```clojure
(defn cached-search [vectordb query k]
  "Поиск с кэшированием результатов"
  (let [cache-key (str "search:" (hash query) ":" k)]
    (if-let [cached-result (cache/get cache-key)]
      cached-result
      (let [result (vdb/search-by-text vectordb query k)]
        (cache/put cache-key result)
        result))))
```

### Батчевые операции

```clojure
(defn add-vectors-batch [vectordb vectors-data]
  "Добавляет множество векторов за одну операцию"
  (reduce (fn [db {:keys [key vector metadata]}]
            (vdb/add-vector db key vector metadata))
          vectordb
          vectors-data))
```

## Мониторинг и метрики

### Метрики производительности

```clojure
(defn track-search-metrics [vectordb query k]
  (let [start-time (System/currentTimeMillis)
        results (vdb/search-by-text vectordb query k)
        end-time (System/currentTimeMillis)
        duration (- end-time start-time)]
    
    ;; Записываем метрики
    (metrics/record-search-time duration)
    (metrics/record-search-results-count (count results))
    
    results))
```

### Мониторинг памяти

```clojure
(defn memory-usage [vectordb]
  "Вычисляет использование памяти векторной БД"
  (let [vectors (:vectors @(:state vectordb))
        metadata (:metadata @(:state vectordb))
        vector-size (* (count vectors) 
                      (count (first (vals vectors))) 
                      8) ; 8 bytes per double
        metadata-size (* (count metadata) 100)] ; примерная оценка
    {:vectors-memory vector-size
     :metadata-memory metadata-size
     :total-memory (+ vector-size metadata-size)}))
```

## Расширения и интеграции

### Внешние векторные БД

Для production использования можно интегрировать с внешними решениями:

```clojure
;; Интеграция с Pinecone
(defrecord PineconeVectorDB [api-key index-name]
  IVectorDatabase
  (add-vector [this key vector metadata]
    ;; Реализация через Pinecone API
    )
  
  (search-similar [this query-vector k]
    ;; Поиск через Pinecone
    ))

;; Интеграция с Weaviate
(defrecord WeaviateVectorDB [endpoint api-key]
  IVectorDatabase
  ;; Реализация методов
  )
```

### Машинное обучение

Интеграция с ML моделями для лучших эмбеддингов:

```clojure
(defn bert-embeddings [text]
  "Получает эмбеддинги через BERT модель"
  ;; Интеграция с Python ML моделью
  ;; через libpython-clj или HTTP API
  )

(defn sentence-transformers-embeddings [text]
  "Использует Sentence Transformers для эмбеддингов"
  ;; Вызов внешнего сервиса
  )
```

## Тестирование

### Unit тесты

```clojure
(deftest test-cosine-similarity
  (testing "Cosine similarity calculation"
    (is (= 1.0 (cosine-similarity [1 0 0] [1 0 0])))
    (is (= 0.0 (cosine-similarity [1 0 0] [0 1 0])))
    (is (< 0.7 (cosine-similarity [1 1 0] [1 0 1]) 0.8))))

(deftest test-vector-operations
  (testing "Adding and retrieving vectors"
    (let [db (create-in-memory-vectordb)]
      (add-vector db "test" [1 2 3] {:type "test"})
      (is (= [1 2 3] (get-vector db "test"))))))
```

### Интеграционные тесты

```clojure
(deftest test-search-integration
  (testing "End-to-end search functionality"
    (let [db (create-test-vectordb-with-data)]
      (let [results (search-by-text db "data processing" 3)]
        (is (= 3 (count results)))
        (is (every? #(> (:similarity %) 0.5) results))))))
```

## Лучшие практики

1. **Нормализация векторов**: Всегда нормализуйте векторы для корректного вычисления косинусного сходства
2. **Размерность**: Выбирайте оптимальную размерность векторов (обычно 100-1000)
3. **Метаданные**: Сохраняйте достаточно метаданных для фильтрации результатов
4. **Индексирование**: Для больших объемов используйте индексы
5. **Мониторинг**: Отслеживайте производительность и точность поиска
