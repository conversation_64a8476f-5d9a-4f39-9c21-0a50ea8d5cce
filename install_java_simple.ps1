# Simple Java Installer Script

# Check if running as admin
$isAdmin = ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script requires administrator privileges. Please run as administrator."
    pause
    exit 1
}

# Download and install Java
$javaUrl = "https://github.com/adoptium/temurin21-binaries/releases/download/jdk-21.0.3+9/OpenJDK21U-jdk_x64_windows_hotspot_21.0.3_9.msi"
$installerPath = "$env:TEMP\java_installer.msi"

Write-Host "Downloading Java..."
try {
    Invoke-WebRequest -Uri $javaUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "Installing Java..."
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$installerPath`" /qn" -Wait -NoNewWindow
    Write-Host "Java installed successfully!"
}
catch {
    Write-Host "Error installing Java: $_"
    if (Test-Path $installerPath) { Remove-Item $installerPath }
    pause
    exit 1
}

# Clean up
if (Test-Path $installerPath) { Remove-Item $installerPath }

Write-Host "Installation complete!"
pause
