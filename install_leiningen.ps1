Write-Host "=== Installing Leiningen ===" -ForegroundColor Green

# Check if Java is installed
try {
    $javaVersion = java -version 2>&1
    Write-Host "✓ Java found: $($javaVersion[0])" -ForegroundColor Green
} catch {
    Write-Host "✗ Java not found. Please install Java 8 or later first." -ForegroundColor Red
    Write-Host "Download from: https://adoptium.net/" -ForegroundColor Yellow
    exit 1
}

# Create lein directory
$leinDir = "$env:USERPROFILE\.lein"
if (-not (Test-Path $leinDir)) {
    New-Item -ItemType Directory -Path $leinDir -Force
    Write-Host "✓ Created .lein directory" -ForegroundColor Green
}

# Download lein.bat
$leinBat = "$leinDir\lein.bat"
$leinUrl = "https://raw.githubusercontent.com/technomancy/leiningen/stable/bin/lein.bat"

Write-Host "Downloading lein.bat..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $leinUrl -OutFile $leinBat
    Write-Host "✓ Downloaded lein.bat" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to download lein.bat: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Add to PATH if not already there
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$leinDir*") {
    Write-Host "Adding Leiningen to PATH..." -ForegroundColor Yellow
    $newPath = "$currentPath;$leinDir"
    [Environment]::SetEnvironmentVariable("PATH", $newPath, "User")
    Write-Host "✓ Added to PATH (restart terminal to take effect)" -ForegroundColor Green
    
    # Also add to current session
    $env:PATH += ";$leinDir"
}

# Test lein installation
Write-Host "Testing Leiningen installation..." -ForegroundColor Yellow
try {
    & "$leinBat" version
    Write-Host "✓ Leiningen installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "✗ Leiningen installation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to restart your terminal and run 'lein version' manually" -ForegroundColor Yellow
}

Write-Host "`n=== Installation Complete ===" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart your terminal (or open new one)" -ForegroundColor White
Write-Host "2. Run: lein version" -ForegroundColor White
Write-Host "3. Run: lein deps (to download dependencies)" -ForegroundColor White
Write-Host "4. Run: lein run (to start the GraphQL server)" -ForegroundColor White
