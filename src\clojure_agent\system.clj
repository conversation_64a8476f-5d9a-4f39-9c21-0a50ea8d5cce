(ns clojure-agent.system
  "Основной системный компонент, управляющий жизненным циклом приложения."
  (:require [com.stuartsierra.component :as component]
            [taoensso.timbre :as log]
            [mount.core :as mount]
            [clojure-agent.config :as config]
            [clojure-agent.db.core :as db]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.graphql.server :as graphql-server]
            [clojure-agent.metrics :as metrics]
            [clojure-agent.cache :as cache]
            [clojure-agent.migrations :as migrations]))

;; Глобальная ссылка на текущую систему
(defonce system (atom nil))

(defn- configure-logging! [log-config]
  "Настройка системы логирования"
  (log/merge-config!
   {:min-level (or (:level log-config) :info)
    :appenders
    (cond-> {}
      (:console? log-config true)
      (assoc :println {:enabled? true})
      
      (:file log-config)
      (assoc :spit (merge {:enabled? true}
                         (select-keys log-config [:file :max-size :max-backup]))))}))

(defn- start-component [component component-name]
  "Безопасный запуск компонента с логированием"
  (log/info "Запуск компонента:" component-name)
  (try
    (let [started (component/start component)]
      (log/info "Компонент успешно запущен:" component-name)
      started)
    (catch Exception e
      (log/error e "Ошибка при запуске компонента:" component-name)
      (throw (ex-info (str "Ошибка запуска компонента: " component-name)
                      {:component component-name}
                      e)))))

(defn- stop-component [component component-name]
  "Безопасная остановка компонента с логированием"
  (when component
    (log/info "Остановка компонента:" component-name)
    (try
      (component/stop component)
      (log/info "Компонент успешно остановлен:" component-name)
      (catch Exception e
        (log/error e "Ошибка при остановке компонента:" component-name)
        (throw (ex-info (str "Ошибка остановки компонента: " component-name)
                        {:component component-name}
                        e))))))

(defrecord ClojureAgentSystem [config logger db agent metrics cache graphql-server]
  component/Lifecycle
  (start [this]
    (log/info "Запуск системы Clojure Agent...")
    (try
      ;; 1. Настройка логирования
      (configure-logging! (:logging config))
      
      ;; 2. Запуск компонентов в правильном порядке
      (let [;; 2.1. Подключаемся к БД
            db (start-component (db/new-db-pool (:database config)) "Database")
            
            ;; 2.2. Запускаем систему метрик
            metrics (start-component (metrics/new-metrics-system (:metrics config)) "Metrics")
            
            ;; 2.3. Инициализируем кэш
            cache (start-component (cache/new-cache-system (:cache config)) "Cache")
            
            ;; 2.4. Запускаем агентную систему
            agent (start-component (agent/new-agent-system (:agent config)) "Agent System")

            ;; 2.5. Создаем систему для GraphQL
            system-map {:db db :agent agent :cache cache :metrics metrics}

            ;; 2.6. Запускаем GraphQL сервер
            graphql-server (start-component
                           (assoc (graphql-server/new-graphql-server (:graphql config))
                                  :system system-map)
                           "GraphQL Server")]
        
        (log/info "Система успешно запущена")
        (assoc this
               :db db
               :metrics metrics
               :cache cache
               :agent agent
               :graphql-server graphql-server))
      
      (catch Exception e
        (log/error e "Критическая ошибка при запуске системы")
        (throw (ex-info "Не удалось запустить систему" {} e)))))
  
  (stop [this]
    (log/info "Остановка системы Clojure Agent...")
    (try
      ;; Останавливаем компоненты в обратном порядке
      (let [stopped (-> this
                        (update :graphql-server #(stop-component % "GraphQL Server"))
                        (update :agent #(stop-component % "Agent System"))
                        (update :cache #(stop-component % "Cache"))
                        (update :metrics #(stop-component % "Metrics"))
                        (update :db #(stop-component % "Database")))]
        (log/info "Система успешно остановлена")
        (assoc stopped :config nil))
      (catch Exception e
        (log/error e "Ошибка при остановке системы")
        (throw (ex-info "Не удалось корректно остановить систему" {} e))))))

(defn new-system
  "Создает новую инстанс системы с указанной конфигурацией."
  [config]
  (map->ClojureAgentSystem {:config config}))

;; ===========================================================================
;; Управление системой
;; ===========================================================================

(defn init
  "Инициализирует систему с конфигурацией по умолчанию."
  ([] (init (config/load-config)))
  ([config]
   (when-let [s @system]
     (log/warn "Переинициализация работающей системы")
     (component/stop s))
   (reset! system (new-system config))))

(defn start
  "Запускает систему, если она еще не запущена."
  []
  (if-let [s @system]
    (do
      (log/warn "Система уже запущена")
      s)
    (do
      (log/info "Запуск системы...")
      (let [s (component/start (or @system (init)))]
        (reset! system s)
        s))))

(defn stop
  "Останавливает систему, если она запущена."
  []
  (when-let [s @system]
    (log/info "Остановка системы...")
    (component/stop s)
    (reset! system nil)
    (log/info "Система остановлена"))
  nil)

(defn restart
  "Перезапускает систему."
  []
  (when @system (stop))
  (start))

;; Автоматическая остановка при перезагрузке кода
(defn on-reload []
  (when @system
    (log/info "Перезагрузка кода, остановка системы...")
    (stop)))

;; Остановка системы при завершении работы JVM
(.addShutdownHook
 (Runtime/getRuntime)
 (Thread. ^Runnable (fn []
                      (log/info "Завершение работы JVM, остановка системы...")
                      (stop))))
