(ns test-api
  (:require [clojure.test :as t]
            [clojure.string :as str]
            [clojure.data.json :as json]
            [ring.mock.request :as mock]
            [clojure-agent.test-helper :as helper]
            [clojure-agent.api.routes :as routes]
            [clojure-agent.system :as system]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.db.core :as db]
            [clojure-agent.metrics :as metrics]))

;; Загружаем тестовую конфигурацию
(def test-config
  {:db {:subprotocol "h2"
        :subname "mem:testdb;DB_CLOSE_DELAY=-1;"
        :user "sa"
        :password ""}
   :http {:port 3000}
   :agent-system {}
   :metrics {:enabled true
             :reporters [{:type :console
                         :interval 60}]}
   :cache {:default-ttl 300
           :type :ttl}
   :log-level :info})

;; Вспомогательные функции для тестирования
(defn parse-json [s]
  (json/read-str s :key-fn keyword))

(defn request
  ([method uri] (request method uri nil nil))
  ([method uri body] (request method uri body nil))
  ([method uri body headers]
   (let [req (-> (mock/request method uri)
                 (mock/content-type "application/json")
                 (mock/header "Accept" "application/json")
                 (mock/header "X-Request-ID" (str (java.util.UUID/randomUUID))))
         req (if body
               (mock/body req (json/write-str body))
               req)
         req (if headers
               (reduce-kv (fn [r k v] (mock/header r (name k) v)) req headers)
               req)]
     req)))

(defn response->data [response]
  (when-let [body (:body response)]
    (if (string? body)
      (parse-json body)
      body)))

;; Тесты
(defn run-tests []
  (println "Запуск тестов API...")
  (let [system (system/start (system/new-system test-config))
        app (routes/app-routes (:db system) (:agent system))]
    
    (println "\n=== Тест 1: Проверка health-эндпоинта ===")
    (let [response (app (request :get "/api/v1/health"))
          body (response->data response)]
      (println "Статус:" (:status response))
      (println "Тело ответа:" (pr-str body))
      (println "Результат:" (if (= 200 (:status response)) "УСПЕХ" "ОШИБКА")))
    
    (println "\n=== Тест 2: Создание нового агента ===")
    (let [agent-data {:id "test-agent" :type :test-type :params {:key "value"}}
          response (app (request :post "/api/v1/agents" agent-data))
          body (response->data response)]
      (println "Статус:" (:status response))
      (println "Тело ответа:" (pr-str body))
      (println "Результат:" (if (and (= 201 (:status response)) (:success body)) "УСПЕХ" "ОШИБКА")))
    
    (println "\n=== Тест 3: Получение информации об агенте ===")
    (let [response (app (request :get "/api/v1/agents/test-agent"))
          body (response->data response)]
      (println "Статус:" (:status response))
      (println "Тело ответа:" (pr-str body))
      (println "Результат:" (if (and (= 200 (:status response)) (:success body)) "УСПЕХ" "ОШИБКА")))
    
    (println "\n=== Тест 4: Отправка сообщения агенту ===")
    (let [message {:type :test :data "test-data"}
          response (app (request :post "/api/v1/agents/test-agent/messages" message))
          body (response->data response)]
      (println "Статус:" (:status response))
      (println "Тело ответа:" (pr-str body))
      (println "Результат:" (if (and (= 200 (:status response)) (:success body)) "УСПЕХ" "ОШИБКА")))
    
    (println "\n=== Тест 5: Получение метрик ===")
    (let [response (app (request :get "/api/v1/metrics"))
          body (response->data response)]
      (println "Статус:" (:status response))
      (println "Метрики доступны:" (not (empty? (get body :data))))
      (println "Результат:" (if (and (= 200 (:status response)) (:success body)) "УСПЕХ" "ОШИБКА")))
    
    (println "\n=== Завершение тестирования ===")
    (system/stop system)))

;; Запуск тестов при загрузке файла
(comment
  (run-tests))
