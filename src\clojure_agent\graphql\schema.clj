(ns clojure-agent.graphql.schema
  "GraphQL схема для агентной системы"
  (:require [com.walmartlabs.lacinia.schema :as schema]
            [com.walmartlabs.lacinia.util :as util]
            [clojure.spec.alpha :as s]
            [clojure.tools.logging :as log]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.metrics :as metrics]
            [clojure-agent.cache :as cache]))

;; ===========================================================================
;; GraphQL типы данных
;; ===========================================================================

(def agent-type
  "Тип данных для агента"
  {:description "Агент в системе"
   :fields {:id {:type (list :non-null :String)
                 :description "Уникальный идентификатор агента"}
            :type {:type (list :non-null :String)
                   :description "Тип агента"}
            :state {:type :JSON
                    :description "Текущее состояние агента"}
            :created_at {:type :String
                         :description "Время создания агента"}
            :updated_at {:type :String
                         :description "Время последнего обновления агента"}}})

(def message-type
  "Тип данных для сообщения"
  {:description "Сообщение для агента"
   :fields {:type {:type (list :non-null :String)
                   :description "Тип сообщения"}
            :payload {:type :JSON
                      :description "Полезная нагрузка сообщения"}
            :timestamp {:type :String
                        :description "Время отправки сообщения"}}})

(def message-result-type
  "Тип данных для результата обработки сообщения"
  {:description "Результат обработки сообщения агентом"
   :fields {:success {:type (list :non-null :Boolean)
                      :description "Успешность обработки"}
            :result {:type :JSON
                     :description "Результат обработки"}
            :error {:type :String
                    :description "Сообщение об ошибке, если обработка неуспешна"}
            :agent_state {:type :JSON
                          :description "Новое состояние агента после обработки"}}})

(def health-status-type
  "Тип данных для статуса здоровья системы"
  {:description "Статус здоровья системы"
   :fields {:status {:type (list :non-null :String)
                     :description "Статус системы (ok, warning, error)"}
            :timestamp {:type (list :non-null :String)
                        :description "Время проверки"}
            :uptime {:type :Int
                     :description "Время работы системы в секундах"}
            :version {:type :String
                      :description "Версия системы"}}})

(def metrics-type
  "Тип данных для метрик системы"
  {:description "Метрики производительности системы"
   :fields {:agents_count {:type :Int
                           :description "Количество активных агентов"}
            :messages_processed {:type :Int
                                 :description "Количество обработанных сообщений"}
            :memory_usage {:type :Float
                           :description "Использование памяти в процентах"}
            :cpu_usage {:type :Float
                        :description "Использование CPU в процентах"}}})

;; ===========================================================================
;; Input типы для мутаций
;; ===========================================================================

(def create-agent-input
  "Входные данные для создания агента"
  {:description "Данные для создания нового агента"
   :fields {:id {:type (list :non-null :String)
                 :description "Уникальный идентификатор агента"}
            :type {:type (list :non-null :String)
                   :description "Тип агента"}
            :initial_state {:type :JSON
                            :description "Начальное состояние агента"}}})

(def send-message-input
  "Входные данные для отправки сообщения"
  {:description "Данные для отправки сообщения агенту"
   :fields {:agent_id {:type (list :non-null :String)
                       :description "ID агента-получателя"}
            :message_type {:type (list :non-null :String)
                           :description "Тип сообщения"}
            :payload {:type :JSON
                      :description "Полезная нагрузка сообщения"}}})

;; ===========================================================================
;; Резолверы (resolvers)
;; ===========================================================================

(defn get-agent-resolver
  "Резолвер для получения агента по ID"
  [context args _]
  (let [agent-system (get-in context [:system :agent])
        agent-id (:id args)]
    (log/debug "Получение агента:" agent-id)
    (try
      (when-let [agent-state (agent/get-agent-state agent-system agent-id)]
        (let [agent-data (get @(:agents agent-system) agent-id)]
          {:id agent-id
           :type (name (:type agent-data))
           :state agent-state
           :created_at (str (java.time.Instant/now))
           :updated_at (str (java.time.Instant/now))}))
      (catch Exception e
        (log/error e "Ошибка при получении агента:" agent-id)
        nil))))

(defn list-agents-resolver
  "Резолвер для получения списка всех агентов"
  [context args _]
  (let [agent-system (get-in context [:system :agent])]
    (log/debug "Получение списка агентов")
    (try
      (let [agent-ids (agent/list-agents agent-system)]
        (map (fn [agent-id]
               (let [agent-data (get @(:agents agent-system) agent-id)
                     agent-state (agent/get-agent-state agent-system agent-id)]
                 {:id agent-id
                  :type (name (:type agent-data))
                  :state agent-state
                  :created_at (str (java.time.Instant/now))
                  :updated_at (str (java.time.Instant/now))}))
             agent-ids))
      (catch Exception e
        (log/error e "Ошибка при получении списка агентов")
        []))))

(defn health-resolver
  "Резолвер для проверки здоровья системы"
  [context args _]
  (log/debug "Проверка здоровья системы")
  {:status "ok"
   :timestamp (str (java.time.Instant/now))
   :uptime 0
   :version "1.0.0"})

(defn metrics-resolver
  "Резолвер для получения метрик системы"
  [context args _]
  (let [agent-system (get-in context [:system :agent])
        metrics-system (get-in context [:system :metrics])]
    (log/debug "Получение метрик системы")
    (try
      (let [agents-count (count (agent/list-agents agent-system))]
        {:agents_count agents-count
         :messages_processed 0
         :memory_usage 0.0
         :cpu_usage 0.0})
      (catch Exception e
        (log/error e "Ошибка при получении метрик")
        {:agents_count 0
         :messages_processed 0
         :memory_usage 0.0
         :cpu_usage 0.0}))))

(defn create-agent-resolver
  "Резолвер для создания нового агента"
  [context args _]
  (let [agent-system (get-in context [:system :agent])
        {:keys [id type initial_state]} (:input args)]
    (log/debug "Создание агента:" id "типа:" type)
    (try
      (let [agent-type-kw (keyword type)
            initial-state (or initial-state {})
            handler-fn (fn [state message] 
                        (merge state (:payload message)))
            created-agent (agent/create-agent agent-system agent-type-kw id initial-state handler-fn)]
        {:id id
         :type type
         :state initial-state
         :created_at (str (java.time.Instant/now))
         :updated_at (str (java.time.Instant/now))})
      (catch Exception e
        (log/error e "Ошибка при создании агента:" id)
        (throw (ex-info "Не удалось создать агента" 
                        {:agent-id id :error (.getMessage e)} e))))))

(defn send-message-resolver
  "Резолвер для отправки сообщения агенту"
  [context args _]
  (let [agent-system (get-in context [:system :agent])
        {:keys [agent_id message_type payload]} (:input args)]
    (log/debug "Отправка сообщения агенту:" agent_id "типа:" message_type)
    (try
      (let [message {::agent/type (keyword message_type)
                     ::agent/payload (or payload {})}
            result (agent/send-message agent-system agent_id message)]
        {:success true
         :result result
         :error nil
         :agent_state result})
      (catch Exception e
        (log/error e "Ошибка при отправке сообщения агенту:" agent_id)
        {:success false
         :result nil
         :error (.getMessage e)
         :agent_state nil}))))

;; ===========================================================================
;; Определение схемы
;; ===========================================================================

(def schema-definition
  "Определение GraphQL схемы"
  {:objects {:Agent agent-type
             :Message message-type
             :MessageResult message-result-type
             :HealthStatus health-status-type
             :Metrics metrics-type}
   
   :input-objects {:CreateAgentInput create-agent-input
                   :SendMessageInput send-message-input}
   
   :queries {:agent {:type :Agent
                     :description "Получить агента по ID"
                     :args {:id {:type (list :non-null :String)}}
                     :resolve :get-agent}
             
             :agents {:type (list :Agent)
                      :description "Получить список всех агентов"
                      :resolve :list-agents}
             
             :health {:type :HealthStatus
                      :description "Проверить здоровье системы"
                      :resolve :health}
             
             :metrics {:type :Metrics
                       :description "Получить метрики системы"
                       :resolve :metrics}}
   
   :mutations {:createAgent {:type :Agent
                             :description "Создать нового агента"
                             :args {:input {:type (list :non-null :CreateAgentInput)}}
                             :resolve :create-agent}
               
               :sendMessage {:type :MessageResult
                             :description "Отправить сообщение агенту"
                             :args {:input {:type (list :non-null :SendMessageInput)}}
                             :resolve :send-message}}
   
   :scalars {:JSON {:description "JSON scalar type"
                    :serialize identity
                    :parse identity}}})

(def resolvers
  "Карта резолверов"
  {:get-agent get-agent-resolver
   :list-agents list-agents-resolver
   :health health-resolver
   :metrics metrics-resolver
   :create-agent create-agent-resolver
   :send-message send-message-resolver})

(defn create-schema
  "Создает скомпилированную GraphQL схему"
  []
  (-> schema-definition
      (util/attach-resolvers resolvers)
      schema/compile))

(defn validate-schema
  "Проверяет валидность GraphQL схемы"
  []
  (try
    (create-schema)
    true
    (catch Exception e
      (log/error e "Ошибка в GraphQL схеме")
      false)))
