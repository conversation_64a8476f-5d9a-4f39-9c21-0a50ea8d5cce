# Clojure Agent System

Современная система агентов на Clojure с поддержкой GraphQL API и векторной базы данных для семантического поиска.

## 🚀 Описание

Это продвинутая система для создания и управления интеллектуальными агентами, написанная на Clojure. Система предоставляет мощный GraphQL API для взаимодействия с агентами и включает в себя векторную базу данных для семантического поиска, систему метрик, кэширование и реляционную базу данных.

## ✨ Основные возможности

### 🤖 Управление агентами
- Создание и управление агентами различных типов
- Отправка сообщений и команд агентам
- Отслеживание состояния агентов в реальном времени
- Поддержка различных типов агентов (worker, manager, analyzer и др.)

### 🔍 Векторный поиск
- Семантический поиск агентов по описанию
- Векторные эмбеддинги для текстового контента
- Поиск похожих агентов на основе косинусного сходства
- Статистика и метрики векторной базы данных

### � GraphQL API
- Современный GraphQL интерфейс
- Интроспекция схемы
- Поддержка запросов (queries) и мутаций (mutations)
- Встроенная валидация и обработка ошибок

### 📊 Мониторинг и метрики
- Система метрик производительности
- Мониторинг здоровья системы
- Отслеживание использования ресурсов
- Логирование всех операций

### 💾 Хранение данных
- H2 база данных для реляционных данных
- Векторная база данных для семантического поиска
- Система миграций базы данных
- Кэширование для повышения производительности

## 🛠 Технологический стек

- **Язык**: Clojure 1.11.1
- **Веб-сервер**: Jetty
- **GraphQL**: Lacinia 1.2.2
- **База данных**: H2 Database + Векторная БД
- **Кэширование**: clojure.core.cache
- **Логирование**: tools.logging
- **Архитектура**: Stuart Sierra Component

## � Требования

- **Java**: 8 или выше
- **Leiningen**: 2.0 или выше
- **Память**: минимум 512MB RAM
- **Диск**: 100MB свободного места

## 🚀 Быстрый старт

### 1. Установка

```bash
# Клонируйте репозиторий
git clone <repository-url>
cd clojure-agent

# Установите зависимости
lein deps
```

### 2. Запуск системы

```bash
# Запуск в режиме разработки
lein run

# Или запуск с профилем production
lein with-profile production run
```

Система будет доступна по адресу: `http://localhost:3000`

### 3. Проверка работоспособности

```bash
# Проверка здоровья системы
curl http://localhost:3000/health

# GraphQL Playground
# Откройте в браузере: http://localhost:3000/graphql
```

## 📖 GraphQL API

### Эндпоинты

- **GraphQL API**: `http://localhost:3000/graphql`
- **GraphQL Playground**: `http://localhost:3000/graphql` (в браузере)
- **Health Check**: `http://localhost:3000/health`

### Основные типы данных

#### Agent
```graphql
type Agent {
  id: String!
  type: String!
  state: JSON
  created_at: String
  updated_at: String
}
```

#### VectorSearchResult
```graphql
type VectorSearchResult {
  key: String!
  similarity: Float
  metadata: JSON
}
```

#### VectorStats
```graphql
type VectorStats {
  total_vectors: Int
  average_vector_size: Float
  memory_usage: String
}
```

### Примеры запросов

#### 🔍 Получение списка агентов
```graphql
query GetAllAgents {
  agents {
    id
    type
    state
    created_at
    updated_at
  }
}
```

#### 🔍 Получение конкретного агента
```graphql
query GetAgent($id: String!) {
  agent(id: $id) {
    id
    type
    state
    created_at
    updated_at
  }
}
```

#### 🔍 Векторный поиск агентов
```graphql
query SearchSimilarAgents($query: String!, $k: Int) {
  vectorSearch(query: $query, k: $k) {
    key
    similarity
    metadata
  }
}
```

#### 🔍 Статистика векторной БД
```graphql
query GetVectorStats {
  vectorStats {
    total_vectors
    average_vector_size
    memory_usage
  }
}
```

#### 🔍 Проверка здоровья системы
```graphql
query HealthCheck {
  health {
    status
    timestamp
    uptime
    version
  }
}
```

#### 🔍 Метрики системы
```graphql
query GetMetrics {
  metrics {
    agents_count
    messages_processed
    memory_usage
    cpu_usage
  }
}
```

### Примеры мутаций

#### ➕ Создание агента
```graphql
mutation CreateAgent($input: CreateAgentInput!) {
  createAgent(input: $input) {
    id
    type
    state
    created_at
    updated_at
  }
}

# Переменные:
{
  "input": {
    "id": "agent-001",
    "type": "worker",
    "initial_state": "{\"status\": \"active\", \"task_queue\": []}"
  }
}
```

#### 📨 Отправка сообщения агенту
```graphql
mutation SendMessage($input: SendMessageInput!) {
  sendMessage(input: $input) {
    success
    result
    error
    agent_state
  }
}

# Переменные:
{
  "input": {
    "agent_id": "agent-001",
    "message_type": "task",
    "payload": "{\"action\": \"process_data\", \"data\": [1,2,3]}"
  }
}
```

## 🏗️ Архитектура системы

### Компоненты

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   GraphQL API   │◄──►│   Agent Core     │◄──►│   Vector DB     │
│   (Lacinia)     │    │   (Component)    │    │   (In-Memory)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   HTTP Server   │    │   H2 Database    │    │   Cache System  │
│   (Jetty)       │    │   (Relational)   │    │   (Memory)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Metrics       │    │   Migrations     │    │   Logging       │
│   (Monitoring)  │    │   (Database)     │    │   (tools.log)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Модули системы

#### 🔧 Ядро системы
- **system.clj** - Главный компонент системы
- **config.clj** - Управление конфигурацией
- **core.clj** - Точка входа приложения

#### 🤖 Управление агентами
- **agent/core.clj** - Основная логика агентов
- **agent/state.clj** - Управление состоянием
- **agent/messages.clj** - Обработка сообщений

#### 🌐 GraphQL API
- **graphql/schema.clj** - Схема GraphQL
- **graphql/resolvers.clj** - Резолверы запросов
- **graphql/server.clj** - HTTP сервер

#### 💾 Хранение данных
- **db/core.clj** - Основная работа с БД
- **db/migrations.clj** - Миграции схемы
- **db/vectordb.clj** - Векторная база данных

#### 📊 Мониторинг
- **metrics.clj** - Система метрик
- **cache.clj** - Кэширование данных

## ⚙️ Конфигурация

### Основные параметры

Конфигурация находится в файле `resources/config.edn`:

```clojure
{:database {:url "jdbc:h2:./data/agents"
            :user "sa"
            :password ""
            :driver "org.h2.Driver"}

 :graphql {:port 3000
           :host "0.0.0.0"
           :path "/graphql"}

 :vectordb {:type :in-memory
            :dimension 100
            :similarity-threshold 0.7}

 :cache {:default-ttl 300000
         :max-size 1000
         :type :basic}

 :metrics {:enabled true
           :collection-interval 60000}

 :logging {:level :info
           :appenders [:console :file]}}
```

### Переменные окружения

```bash
# Порт сервера
CLOJURE_AGENT_PORT=3000

# Уровень логирования
CLOJURE_AGENT_LOG_LEVEL=info

# Путь к базе данных
CLOJURE_AGENT_DB_PATH=./data/agents

# Включение метрик
CLOJURE_AGENT_METRICS_ENABLED=true
```

## 🔧 Разработка

### Настройка среды разработки

```bash
# Клонирование репозитория
git clone <repository-url>
cd clojure-agent

# Установка зависимостей
lein deps

# Запуск в режиме разработки
lein run

# Запуск REPL
lein repl
```

### Тестирование

```bash
# Запуск всех тестов
lein test

# Запуск конкретного теста
lein test clojure-agent.agent.core-test

# Запуск тестов с покрытием
lein cloverage
```

### Линтинг и форматирование

```bash
# Проверка стиля кода
lein kibit

# Форматирование кода
lein cljfmt fix

# Анализ зависимостей
lein deps :tree
```

## 🚀 Развертывание

### Production сборка

```bash
# Создание uberjar
lein uberjar

# Запуск production версии
java -jar target/clojure-agent-standalone.jar
```

### Docker

```dockerfile
FROM openjdk:11-jre-slim

COPY target/clojure-agent-standalone.jar /app/app.jar

EXPOSE 3000

CMD ["java", "-jar", "/app/app.jar"]
```

```bash
# Сборка образа
docker build -t clojure-agent .

# Запуск контейнера
docker run -p 3000:3000 clojure-agent
```

### Systemd сервис

```ini
[Unit]
Description=Clojure Agent System
After=network.target

[Service]
Type=simple
User=clojure-agent
WorkingDirectory=/opt/clojure-agent
ExecStart=/usr/bin/java -jar clojure-agent-standalone.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## 📊 Мониторинг и метрики

### Доступные метрики

- **Агенты**: количество активных агентов, созданных, удаленных
- **Сообщения**: обработанные сообщения, ошибки, время обработки
- **База данных**: количество запросов, время выполнения, ошибки
- **Векторный поиск**: количество поисков, время поиска, точность
- **Система**: использование памяти, CPU, время работы

### Health Check

```bash
curl http://localhost:3000/health
```

Ответ:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": "2h 15m 30s",
  "version": "1.0.0",
  "components": {
    "database": "healthy",
    "vectordb": "healthy",
    "cache": "healthy",
    "metrics": "healthy"
  }
}
```

## 🔒 Безопасность

### Рекомендации для production

- Настройте HTTPS для GraphQL API
- Добавьте аутентификацию и авторизацию
- Ограничьте сложность GraphQL запросов
- Настройте rate limiting
- Используйте безопасные настройки базы данных
- Регулярно обновляйте зависимости

### Аудит безопасности

```bash
# Проверка уязвимостей в зависимостях
lein nvd check

# Анализ кода на безопасность
lein eastwood
```

## 🐛 Отладка и логирование

### Уровни логирования

- **ERROR**: Критические ошибки
- **WARN**: Предупреждения
- **INFO**: Информационные сообщения
- **DEBUG**: Отладочная информация
- **TRACE**: Детальная трассировка

### Просмотр логов

```bash
# Логи в консоли
tail -f logs/clojure-agent.log

# Фильтрация по уровню
grep "ERROR" logs/clojure-agent.log

# Логи GraphQL запросов
grep "GraphQL" logs/clojure-agent.log
```

## 📚 Дополнительная документация

- [API Reference](docs/api.md) - Полная документация API
- [Architecture Guide](docs/architecture.md) - Подробное описание архитектуры
- [Development Guide](docs/development.md) - Руководство для разработчиков
- [Deployment Guide](docs/deployment.md) - Руководство по развертыванию
- [Vector Database Guide](docs/vectordb.md) - Работа с векторной БД

## 🤝 Вклад в проект

### Как внести вклад

1. Fork репозитория
2. Создайте feature branch (`git checkout -b feature/amazing-feature`)
3. Commit изменения (`git commit -m 'Add amazing feature'`)
4. Push в branch (`git push origin feature/amazing-feature`)
5. Создайте Pull Request

### Стандарты кода

- Следуйте Clojure Style Guide
- Добавляйте тесты для нового функционала
- Обновляйте документацию
- Используйте осмысленные commit сообщения

## 📄 Лицензия

MIT License - см. файл [LICENSE](LICENSE) для подробностей.

## 🙏 Благодарности

- [Clojure](https://clojure.org/) - за прекрасный язык
- [Lacinia](https://lacinia.readthedocs.io/) - за GraphQL библиотеку
- [Stuart Sierra Component](https://github.com/stuartsierra/component) - за архитектуру компонентов
- Сообществу Clojure за поддержку и вдохновение

---

**Clojure Agent System** - современное решение для управления агентами с мощными возможностями поиска и мониторинга. 🚀
