# Clojure Agent System

Высоконадежная агентная система, построенная на Clojure с использованием функционального подхода и неизменяемых структур данных.

## 🚀 Возможности

- Создание и управление агентами
- Асинхронная обработка сообщений
- Транзакционное управление состоянием
- REST API для взаимодействия с системой
- Поддержка различных бэкендов хранения данных

## 🏗️ Архитектура

Система построена по модульному принципу с использованием компонентов:

- **Ядро агентов** - управление жизненным циклом агентов
- **API слой** - HTTP API для взаимодействия с системой
- **Слой данных** - работа с хранилищами (БД, in-memory)
- **Конфигурация** - централизованное управление настройками

## 🛠️ Требования

- Java 17 или новее
- Leiningen 2.9.0 или новее
- Доступ к репозиториям Maven

## 🚀 Быстрый старт

1. Клонируйте репозиторий
2. Установите зависимости:
   ```bash
   lein deps
   ```
3. Настройте конфигурацию в `resources/config.edn`
4. Запустите приложение:
   ```bash
   lein run
   ```

## 📚 Документация

- [Техническое задание](CLOJURE_AGENT_SPEC.md)
- [API документация](docs/api.md) (будет сгенерирована)
- [Руководство разработчика](docs/development.md)

## 📦 Зависимости

- Clojure 1.11.1
- Component для управления зависимостями
- Ring/Compojure для HTTP API
- core.async для асинхронных операций
- next.jdbc для работы с БД

## 📝 Лицензия

EPL-2.0

## 🤝 Вклад

Приветствуются пул-реквесты и сообщения об ошибках.
