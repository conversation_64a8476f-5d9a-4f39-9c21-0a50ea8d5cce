(ns clojure-agent.db.vectordb
  "Модуль для работы с векторной базой данных"
  (:require [com.stuartsierra.component :as component]
            [clojure.tools.logging :as log]
            [clojure.string :as str]
            [clojure.java.io :as io]))

;; Временная реализация векторной БД до интеграции с clojure-vectordb
;; Это будет заменено на полную интеграцию с https://github.com/kroq86/clojure-vectordb/

;; Утилитарные функции
(defn cosine-similarity
  "Вычисляет косинусное сходство между двумя векторами"
  [v1 v2]
  (when (and v1 v2 (= (count v1) (count v2)))
    (let [dot-product (reduce + (map * v1 v2))
          magnitude1 (Math/sqrt (reduce + (map #(* % %) v1)))
          magnitude2 (Math/sqrt (reduce + (map #(* % %) v2)))]
      (if (and (> magnitude1 0) (> magnitude2 0))
        (/ dot-product (* magnitude1 magnitude2))
        0.0))))

(defn simple-text-embedding
  "Простая функция для создания эмбеддингов из текста (заглушка)"
  [text]
  (let [chars (map int (take 100 (cycle text)))
        normalized (map #(/ % 255.0) chars)]
    (vec (take 100 normalized))))

(defprotocol IVectorDatabase
  "Протокол для работы с векторной базой данных"
  (store-vector [this key vector metadata] "Сохранить вектор с метаданными")
  (search-similar [this query-vector k] "Найти k наиболее похожих векторов")
  (search-by-text [this query-text k] "Найти векторы по текстовому запросу")
  (get-vector [this key] "Получить вектор по ключу")
  (delete-vector [this key] "Удалить вектор по ключу")
  (get-all-keys [this] "Получить все ключи")
  (get-stats [this] "Получить статистику базы данных"))

;; Простая реализация в памяти для начала
(defrecord InMemoryVectorDB [vectors embeddings-fn]
  component/Lifecycle
  (start [this]
    (log/info "Запуск векторной базы данных в памяти...")
    (assoc this :vectors (atom {})))
  
  (stop [this]
    (log/info "Остановка векторной базы данных...")
    (when-let [v (:vectors this)]
      (reset! v {}))
    (assoc this :vectors nil))
  
  IVectorDatabase
  (store-vector [this key vector metadata]
    (when-let [vectors (:vectors this)]
      (swap! vectors assoc key {:vector vector
                                :metadata metadata
                                :timestamp (System/currentTimeMillis)})
      true))
  
  (search-similar [this query-vector k]
    (when-let [vectors (:vectors this)]
      (let [all-vectors @vectors]
        (->> all-vectors
             (map (fn [[key data]]
                    (let [similarity (cosine-similarity query-vector (:vector data))]
                      {:key key
                       :similarity similarity
                       :metadata (:metadata data)})))
             (sort-by :similarity >)
             (take k)))))
  
  (search-by-text [this query-text k]
    (when embeddings-fn
      (let [query-vector (embeddings-fn query-text)]
        (search-similar this query-vector k))))
  
  (get-vector [this key]
    (when-let [vectors (:vectors this)]
      (get @vectors key)))
  
  (delete-vector [this key]
    (when-let [vectors (:vectors this)]
      (swap! vectors dissoc key)
      true))
  
  (get-all-keys [this]
    (when-let [vectors (:vectors this)]
      (keys @vectors)))
  
  (get-stats [this]
    (when-let [vectors (:vectors this)]
      (let [all-vectors @vectors
            count (count all-vectors)
            avg-vector-size (if (> count 0)
                              (/ (reduce + (map #(count (:vector %)) (vals all-vectors))) count)
                              0)]
        {:total-vectors count
         :average-vector-size avg-vector-size
         :memory-usage (str (* count avg-vector-size 8) " bytes")}))))

;; Конструктор компонента
(defn new-vector-database
  "Создает новый компонент векторной базы данных"
  ([]
   (new-vector-database simple-text-embedding))
  ([embeddings-fn]
   (map->InMemoryVectorDB {:embeddings-fn embeddings-fn})))

;; Функции для работы с агентами через векторную БД
(defn store-agent-embedding
  "Сохраняет эмбеддинг агента в векторной БД"
  [vectordb agent-id agent-data]
  (let [text-content (str (:type agent-data) " " (:description agent-data ""))
        metadata {:agent-id agent-id
                  :type (:type agent-data)
                  :created-at (:created-at agent-data)
                  :updated-at (:updated-at agent-data)}]
    (if-let [embeddings-fn (:embeddings-fn vectordb)]
      (let [vector (embeddings-fn text-content)]
        (store-vector vectordb agent-id vector metadata))
      (log/warn "Функция эмбеддингов не настроена"))))

(defn find-similar-agents
  "Находит похожих агентов по описанию"
  [vectordb query-text k]
  (search-by-text vectordb query-text k))

(defn get-agent-embedding
  "Получает эмбеддинг агента"
  [vectordb agent-id]
  (get-vector vectordb agent-id))

(defn remove-agent-embedding
  "Удаляет эмбеддинг агента"
  [vectordb agent-id]
  (delete-vector vectordb agent-id))
