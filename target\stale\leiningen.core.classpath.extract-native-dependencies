[{:dependencies {com.cognitect/transit-java {:vsn "1.0.343", :native-prefix nil}, metosin/reitit-middleware {:vsn "0.5.18", :native-prefix nil}, org.clojure/data.json {:vsn "2.4.0", :native-prefix nil}, org.clojure/clojure {:vsn "1.11.1", :native-prefix nil}, com.ibm.icu/icu4j {:vsn "69.1", :native-prefix nil}, com.rpl/proxy-plus {:vsn "0.0.9", :native-prefix nil}, org.clojure/tools.analyzer {:vsn "1.1.0", :native-prefix nil}, com.zaxxer/HikariCP {:vsn "5.0.1", :native-prefix nil}, org.eclipse.jetty/jetty-servlet {:vsn "9.4.51.v20230217", :native-prefix nil}, org.clojure/tools.logging {:vsn "1.1.0", :native-prefix nil}, org.nrepl/incomplete {:vsn "0.1.0", :native-prefix nil}, org.clojure/core.specs.alpha {:vsn "0.2.62", :native-prefix nil}, hikari-cp {:vsn "3.0.1", :native-prefix nil}, metosin/muuntaja {:vsn "0.6.8", :native-prefix nil}, io.pedestal/pedestal.jetty {:vsn "0.6.0", :native-prefix nil}, metosin/ring-swagger-ui {:vsn "4.3.0", :native-prefix nil}, com.fasterxml.jackson.core/jackson-databind {:vsn "********", :native-prefix nil}, org.clojure/spec.alpha {:vsn "0.3.218", :native-prefix nil}, org.antlr/antlr4-runtime {:vsn "4.9.3", :native-prefix nil}, org.eclipse.jetty.http2/http2-server {:vsn "9.4.51.v20230217", :native-prefix nil}, mvxcvi/puget {:vsn "1.3.4", :native-prefix nil}, crypto-random {:vsn "1.2.1", :native-prefix nil}, com.walmartlabs/lacinia-pedestal {:vsn "1.2", :native-prefix nil}, org.eclipse.jetty/jetty-http {:vsn "9.4.48.v20220622", :native-prefix nil}, org.eclipse.jetty/jetty-util {:vsn "9.4.48.v20220622", :native-prefix nil}, com.taoensso/encore {:vsn "3.60.0", :native-prefix nil}, org.slf4j/jcl-over-slf4j {:vsn "2.0.9", :native-prefix nil}, metosin/spec-tools {:vsn "0.10.5", :native-prefix nil}, compojure {:vsn "1.7.0", :native-prefix nil}, org.clojure/tools.analyzer.jvm {:vsn "1.2.2", :native-prefix nil}, riddley {:vsn "0.1.12", :native-prefix nil}, commons-fileupload {:vsn "1.4", :native-prefix nil}, camel-snake-kebab {:vsn "0.4.3", :native-prefix nil}, io.pedestal/pedestal.log {:vsn "0.6.0", :native-prefix nil}, org.clojure/tools.macro {:vsn "0.1.5", :native-prefix nil}, org.antlr/ST4 {:vsn "4.3.1", :native-prefix nil}, lambdaisland/deep-diff {:vsn "0.0-47", :native-prefix nil}, com.fasterxml.jackson.dataformat/jackson-dataformat-cbor {:vsn "2.10.2", :native-prefix nil}, io.pedestal/pedestal.service {:vsn "0.6.0", :native-prefix nil}, com.googlecode.json-simple/json-simple {:vsn "1.1.1", :native-prefix nil}, io.aviso/pretty {:vsn "1.1.1", :native-prefix nil}, com.h2database/h2 {:vsn "2.2.224", :native-prefix nil}, fipp {:vsn "0.6.25", :native-prefix nil}, org.abego.treelayout/org.abego.treelayout.core {:vsn "1.0.3", :native-prefix nil}, ch.qos.logback/logback-core {:vsn "1.4.11", :native-prefix nil}, aero {:vsn "1.1.6", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-api {:vsn "9.4.51.v20230217", :native-prefix nil}, ring/ring-jetty-adapter {:vsn "1.9.6", :native-prefix nil}, org.eclipse.jetty.alpn/alpn-api {:vsn "1.1.3.v20160715", :native-prefix nil}, ring/ring-json {:vsn "0.5.1", :native-prefix nil}, com.stuartsierra/component {:vsn "1.1.0", :native-prefix nil}, org.flatland/ordered {:vsn "1.15.10", :native-prefix nil}, medley {:vsn "1.4.0", :native-prefix nil}, org.clojure/tools.namespace {:vsn "1.3.0", :native-prefix nil}, org.ow2.asm/asm-util {:vsn "6.2", :native-prefix nil}, org.eclipse.jetty.http2/http2-hpack {:vsn "9.4.51.v20230217", :native-prefix nil}, com.walmartlabs/lacinia {:vsn "1.2.2", :native-prefix nil}, metosin/reitit-swagger {:vsn "0.5.18", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-client {:vsn "9.4.51.v20230217", :native-prefix nil}, com.fasterxml.jackson.core/jackson-core {:vsn "2.13.2", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-servlet {:vsn "9.4.51.v20230217", :native-prefix nil}, org.tobereplaced/lettercase {:vsn "1.0.0", :native-prefix nil}, org.ow2.asm/asm {:vsn "9.2", :native-prefix nil}, org.slf4j/jul-to-slf4j {:vsn "2.0.9", :native-prefix nil}, metosin/reitit-ring {:vsn "0.5.18", :native-prefix nil}, cheshire {:vsn "5.10.0", :native-prefix nil}, metrics-clojure {:vsn "2.10.0", :native-prefix nil}, meta-merge {:vsn "1.0.0", :native-prefix nil}, org.eclipse.jetty/jetty-security {:vsn "9.4.51.v20230217", :native-prefix nil}, io.pedestal/pedestal.interceptor {:vsn "0.6.0", :native-prefix nil}, mvxcvi/arrangement {:vsn "2.1.0", :native-prefix nil}, io.dropwizard.metrics/metrics-core {:vsn "3.2.2", :native-prefix nil}, org.eclipse.jetty/jetty-util-ajax {:vsn "9.4.51.v20230217", :native-prefix nil}, progrock {:vsn "0.1.2", :native-prefix nil}, metosin/reitit-core {:vsn "0.5.18", :native-prefix nil}, com.taoensso/truss {:vsn "1.9.0", :native-prefix nil}, crypto-equality {:vsn "1.0.1", :native-prefix nil}, org.antlr/antlr-runtime {:vsn "3.5.2", :native-prefix nil}, com.fasterxml.jackson.core/jackson-annotations {:vsn "2.13.2", :native-prefix nil}, pjstadig/humane-test-output {:vsn "0.11.0", :native-prefix nil}, io.opentracing/opentracing-api {:vsn "0.33.0", :native-prefix nil}, suspendable {:vsn "0.1.1", :native-prefix nil}, ring-cors {:vsn "0.1.13", :native-prefix nil}, org.javassist/javassist {:vsn "3.18.1-GA", :native-prefix nil}, org.clojure/java.classpath {:vsn "1.0.0", :native-prefix nil}, commons-codec {:vsn "1.15", :native-prefix nil}, com.fzakaria/slf4j-timbre {:vsn "0.3.21", :native-prefix nil}, metosin/reitit-schema {:vsn "0.5.18", :native-prefix nil}, borkdude/dynaload {:vsn "0.2.2", :native-prefix nil}, org.eclipse.jetty.http2/http2-common {:vsn "9.4.51.v20230217", :native-prefix nil}, org.msgpack/msgpack {:vsn "0.6.12", :native-prefix nil}, borkdude/edamame {:vsn "0.0.19", :native-prefix nil}, com.taoensso/timbre {:vsn "6.2.1", :native-prefix nil}, reloaded.repl {:vsn "0.2.4", :native-prefix nil}, metosin/malli {:vsn "0.8.2", :native-prefix nil}, com.cognitect/transit-clj {:vsn "1.0.324", :native-prefix nil}, com.github.seancorfield/next.jdbc {:vsn "1.3.894", :native-prefix nil}, io.opentracing/opentracing-noop {:vsn "0.33.0", :native-prefix nil}, org.ow2.asm/asm-commons {:vsn "6.2", :native-prefix nil}, migratus {:vsn "1.4.9", :native-prefix nil}, ring/ring-codec {:vsn "1.2.0", :native-prefix nil}, metosin/reitit-malli {:vsn "0.5.18", :native-prefix nil}, metosin/schema-tools {:vsn "0.12.3", :native-prefix nil}, org.clojure/core.rrb-vector {:vsn "0.0.14", :native-prefix nil}, prismatic/schema {:vsn "1.1.12", :native-prefix nil}, com.bhauman/spell-spec {:vsn "0.1.2", :native-prefix nil}, instaparse {:vsn "1.4.8", :native-prefix nil}, eftest {:vsn "0.6.0", :native-prefix nil}, clout {:vsn "2.2.1", :native-prefix nil}, org.eclipse.jetty/jetty-client {:vsn "9.4.51.v20230217", :native-prefix nil}, metosin/reitit-sieppari {:vsn "0.5.18", :native-prefix nil}, org.clojure/core.match {:vsn "1.0.1", :native-prefix nil}, org.eclipse.jetty/jetty-io {:vsn "9.4.48.v20220622", :native-prefix nil}, org.clojure/tools.reader {:vsn "1.3.6", :native-prefix nil}, nrepl {:vsn "1.0.0", :native-prefix nil}, io.opentracing/opentracing-util {:vsn "0.33.0", :native-prefix nil}, tigris {:vsn "0.1.2", :native-prefix nil}, javax.servlet/javax.servlet-api {:vsn "3.1.0", :native-prefix nil}, metosin/reitit-http {:vsn "0.5.18", :native-prefix nil}, com.rpl/specter {:vsn "1.1.3", :native-prefix nil}, org.slf4j/slf4j-api {:vsn "1.7.30", :native-prefix nil}, expound {:vsn "0.9.0", :native-prefix nil}, org.clojure/test.check {:vsn "1.1.1", :native-prefix nil}, metosin/reitit-frontend {:vsn "0.5.18", :native-prefix nil}, clj-antlr {:vsn "0.2.12", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-server {:vsn "9.4.51.v20230217", :native-prefix nil}, io.dropwizard.metrics/metrics-jmx {:vsn "4.2.18", :native-prefix nil}, org.ow2.asm/asm-analysis {:vsn "6.2", :native-prefix nil}, metosin/reitit-spec {:vsn "0.5.18", :native-prefix nil}, org.eclipse.jetty.websocket/websocket-common {:vsn "9.4.51.v20230217", :native-prefix nil}, metosin/jsonista {:vsn "0.3.1", :native-prefix nil}, io.pedestal/pedestal.route {:vsn "0.6.0", :native-prefix nil}, metosin/reitit-swagger-ui {:vsn "0.5.18", :native-prefix nil}, metosin/reitit {:vsn "0.5.18", :native-prefix nil}, ring/ring-servlet {:vsn "1.9.6", :native-prefix nil}, ch.qos.logback/logback-classic {:vsn "1.4.11", :native-prefix nil}, org.clojure/core.memoize {:vsn "1.0.253", :native-prefix nil}, com.stuartsierra/dependency {:vsn "1.0.0", :native-prefix nil}, tech.droit/clj-diff {:vsn "1.0.1", :native-prefix nil}, org.clojure/data.priority-map {:vsn "1.1.0", :native-prefix nil}, cider/piggieback {:vsn "0.5.3", :native-prefix nil}, org.clojure/java.data {:vsn "1.0.95", :native-prefix nil}, org.antlr/antlr4 {:vsn "4.9.3", :native-prefix nil}, org.eclipse.jetty/jetty-server {:vsn "9.4.48.v20220622", :native-prefix nil}, commons-io {:vsn "2.11.0", :native-prefix nil}, org.slf4j/log4j-over-slf4j {:vsn "2.0.9", :native-prefix nil}, metosin/reitit-dev {:vsn "0.5.18", :native-prefix nil}, ring/ring-core {:vsn "1.9.6", :native-prefix nil}, org.clojure/core.cache {:vsn "1.0.225", :native-prefix nil}, org.ow2.asm/asm-tree {:vsn "6.2", :native-prefix nil}, org.clojure/core.async {:vsn "1.6.673", :native-prefix nil}, com.fasterxml.jackson.dataformat/jackson-dataformat-smile {:vsn "2.10.2", :native-prefix nil}, com.fasterxml.jackson.datatype/jackson-datatype-jsr310 {:vsn "2.12.0", :native-prefix nil}, org.glassfish/javax.json {:vsn "1.0.4", :native-prefix nil}, javax.xml.bind/jaxb-api {:vsn "2.3.0", :native-prefix nil}, metosin/sieppari {:vsn "0.0.0-alpha13", :native-prefix nil}, metosin/reitit-interceptors {:vsn "0.5.18", :native-prefix nil}, org.eclipse.jetty/jetty-alpn-server {:vsn "9.4.51.v20230217", :native-prefix nil}}, :native-path "target/native"} {:native-path "target/native", :dependencies {com.cognitect/transit-java {:vsn "1.0.343", :native-prefix nil, :native? false}, metosin/reitit-middleware {:vsn "0.5.18", :native-prefix nil, :native? false}, org.clojure/data.json {:vsn "2.4.0", :native-prefix nil, :native? false}, org.clojure/clojure {:vsn "1.11.1", :native-prefix nil, :native? false}, com.ibm.icu/icu4j {:vsn "69.1", :native-prefix nil, :native? false}, com.rpl/proxy-plus {:vsn "0.0.9", :native-prefix nil, :native? false}, org.clojure/tools.analyzer {:vsn "1.1.0", :native-prefix nil, :native? false}, com.zaxxer/HikariCP {:vsn "5.0.1", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-servlet {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, org.clojure/tools.logging {:vsn "1.1.0", :native-prefix nil, :native? false}, org.nrepl/incomplete {:vsn "0.1.0", :native-prefix nil, :native? false}, org.clojure/core.specs.alpha {:vsn "0.2.62", :native-prefix nil, :native? false}, hikari-cp {:vsn "3.0.1", :native-prefix nil, :native? false}, metosin/muuntaja {:vsn "0.6.8", :native-prefix nil, :native? false}, io.pedestal/pedestal.jetty {:vsn "0.6.0", :native-prefix nil, :native? false}, metosin/ring-swagger-ui {:vsn "4.3.0", :native-prefix nil, :native? false}, com.fasterxml.jackson.core/jackson-databind {:vsn "********", :native-prefix nil, :native? false}, org.clojure/spec.alpha {:vsn "0.3.218", :native-prefix nil, :native? false}, org.antlr/antlr4-runtime {:vsn "4.9.3", :native-prefix nil, :native? false}, org.eclipse.jetty.http2/http2-server {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, mvxcvi/puget {:vsn "1.3.4", :native-prefix nil, :native? false}, crypto-random {:vsn "1.2.1", :native-prefix nil, :native? false}, com.walmartlabs/lacinia-pedestal {:vsn "1.2", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-http {:vsn "9.4.48.v20220622", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-util {:vsn "9.4.48.v20220622", :native-prefix nil, :native? false}, com.taoensso/encore {:vsn "3.60.0", :native-prefix nil, :native? false}, org.slf4j/jcl-over-slf4j {:vsn "2.0.9", :native-prefix nil, :native? false}, metosin/spec-tools {:vsn "0.10.5", :native-prefix nil, :native? false}, compojure {:vsn "1.7.0", :native-prefix nil, :native? false}, org.clojure/tools.analyzer.jvm {:vsn "1.2.2", :native-prefix nil, :native? false}, riddley {:vsn "0.1.12", :native-prefix nil, :native? false}, commons-fileupload {:vsn "1.4", :native-prefix nil, :native? false}, camel-snake-kebab {:vsn "0.4.3", :native-prefix nil, :native? false}, io.pedestal/pedestal.log {:vsn "0.6.0", :native-prefix nil, :native? false}, org.clojure/tools.macro {:vsn "0.1.5", :native-prefix nil, :native? false}, org.antlr/ST4 {:vsn "4.3.1", :native-prefix nil, :native? false}, lambdaisland/deep-diff {:vsn "0.0-47", :native-prefix nil, :native? false}, com.fasterxml.jackson.dataformat/jackson-dataformat-cbor {:vsn "2.10.2", :native-prefix nil, :native? false}, io.pedestal/pedestal.service {:vsn "0.6.0", :native-prefix nil, :native? false}, com.googlecode.json-simple/json-simple {:vsn "1.1.1", :native-prefix nil, :native? false}, io.aviso/pretty {:vsn "1.1.1", :native-prefix nil, :native? false}, com.h2database/h2 {:vsn "2.2.224", :native-prefix nil, :native? false}, fipp {:vsn "0.6.25", :native-prefix nil, :native? false}, org.abego.treelayout/org.abego.treelayout.core {:vsn "1.0.3", :native-prefix nil, :native? false}, ch.qos.logback/logback-core {:vsn "1.4.11", :native-prefix nil, :native? false}, aero {:vsn "1.1.6", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-api {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, ring/ring-jetty-adapter {:vsn "1.9.6", :native-prefix nil, :native? false}, org.eclipse.jetty.alpn/alpn-api {:vsn "1.1.3.v20160715", :native-prefix nil, :native? false}, ring/ring-json {:vsn "0.5.1", :native-prefix nil, :native? false}, com.stuartsierra/component {:vsn "1.1.0", :native-prefix nil, :native? false}, org.flatland/ordered {:vsn "1.15.10", :native-prefix nil, :native? false}, medley {:vsn "1.4.0", :native-prefix nil, :native? false}, org.clojure/tools.namespace {:vsn "1.3.0", :native-prefix nil, :native? false}, org.ow2.asm/asm-util {:vsn "6.2", :native-prefix nil, :native? false}, org.eclipse.jetty.http2/http2-hpack {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, com.walmartlabs/lacinia {:vsn "1.2.2", :native-prefix nil, :native? false}, metosin/reitit-swagger {:vsn "0.5.18", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-client {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, com.fasterxml.jackson.core/jackson-core {:vsn "2.13.2", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-servlet {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, org.tobereplaced/lettercase {:vsn "1.0.0", :native-prefix nil, :native? false}, org.ow2.asm/asm {:vsn "9.2", :native-prefix nil, :native? false}, org.slf4j/jul-to-slf4j {:vsn "2.0.9", :native-prefix nil, :native? false}, metosin/reitit-ring {:vsn "0.5.18", :native-prefix nil, :native? false}, cheshire {:vsn "5.10.0", :native-prefix nil, :native? false}, metrics-clojure {:vsn "2.10.0", :native-prefix nil, :native? false}, meta-merge {:vsn "1.0.0", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-security {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, io.pedestal/pedestal.interceptor {:vsn "0.6.0", :native-prefix nil, :native? false}, mvxcvi/arrangement {:vsn "2.1.0", :native-prefix nil, :native? false}, io.dropwizard.metrics/metrics-core {:vsn "3.2.2", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-util-ajax {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, progrock {:vsn "0.1.2", :native-prefix nil, :native? false}, metosin/reitit-core {:vsn "0.5.18", :native-prefix nil, :native? false}, com.taoensso/truss {:vsn "1.9.0", :native-prefix nil, :native? false}, crypto-equality {:vsn "1.0.1", :native-prefix nil, :native? false}, org.antlr/antlr-runtime {:vsn "3.5.2", :native-prefix nil, :native? false}, com.fasterxml.jackson.core/jackson-annotations {:vsn "2.13.2", :native-prefix nil, :native? false}, pjstadig/humane-test-output {:vsn "0.11.0", :native-prefix nil, :native? false}, io.opentracing/opentracing-api {:vsn "0.33.0", :native-prefix nil, :native? false}, suspendable {:vsn "0.1.1", :native-prefix nil, :native? false}, ring-cors {:vsn "0.1.13", :native-prefix nil, :native? false}, org.javassist/javassist {:vsn "3.18.1-GA", :native-prefix nil, :native? false}, org.clojure/java.classpath {:vsn "1.0.0", :native-prefix nil, :native? false}, commons-codec {:vsn "1.15", :native-prefix nil, :native? false}, com.fzakaria/slf4j-timbre {:vsn "0.3.21", :native-prefix nil, :native? false}, metosin/reitit-schema {:vsn "0.5.18", :native-prefix nil, :native? false}, borkdude/dynaload {:vsn "0.2.2", :native-prefix nil, :native? false}, org.eclipse.jetty.http2/http2-common {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, org.msgpack/msgpack {:vsn "0.6.12", :native-prefix nil, :native? false}, borkdude/edamame {:vsn "0.0.19", :native-prefix nil, :native? false}, com.taoensso/timbre {:vsn "6.2.1", :native-prefix nil, :native? false}, reloaded.repl {:vsn "0.2.4", :native-prefix nil, :native? false}, metosin/malli {:vsn "0.8.2", :native-prefix nil, :native? false}, com.cognitect/transit-clj {:vsn "1.0.324", :native-prefix nil, :native? false}, com.github.seancorfield/next.jdbc {:vsn "1.3.894", :native-prefix nil, :native? false}, io.opentracing/opentracing-noop {:vsn "0.33.0", :native-prefix nil, :native? false}, org.ow2.asm/asm-commons {:vsn "6.2", :native-prefix nil, :native? false}, migratus {:vsn "1.4.9", :native-prefix nil, :native? false}, ring/ring-codec {:vsn "1.2.0", :native-prefix nil, :native? false}, metosin/reitit-malli {:vsn "0.5.18", :native-prefix nil, :native? false}, metosin/schema-tools {:vsn "0.12.3", :native-prefix nil, :native? false}, org.clojure/core.rrb-vector {:vsn "0.0.14", :native-prefix nil, :native? false}, prismatic/schema {:vsn "1.1.12", :native-prefix nil, :native? false}, com.bhauman/spell-spec {:vsn "0.1.2", :native-prefix nil, :native? false}, instaparse {:vsn "1.4.8", :native-prefix nil, :native? false}, eftest {:vsn "0.6.0", :native-prefix nil, :native? false}, clout {:vsn "2.2.1", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-client {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, metosin/reitit-sieppari {:vsn "0.5.18", :native-prefix nil, :native? false}, org.clojure/core.match {:vsn "1.0.1", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-io {:vsn "9.4.48.v20220622", :native-prefix nil, :native? false}, org.clojure/tools.reader {:vsn "1.3.6", :native-prefix nil, :native? false}, nrepl {:vsn "1.0.0", :native-prefix nil, :native? false}, io.opentracing/opentracing-util {:vsn "0.33.0", :native-prefix nil, :native? false}, tigris {:vsn "0.1.2", :native-prefix nil, :native? false}, javax.servlet/javax.servlet-api {:vsn "3.1.0", :native-prefix nil, :native? false}, metosin/reitit-http {:vsn "0.5.18", :native-prefix nil, :native? false}, com.rpl/specter {:vsn "1.1.3", :native-prefix nil, :native? false}, org.slf4j/slf4j-api {:vsn "1.7.30", :native-prefix nil, :native? false}, expound {:vsn "0.9.0", :native-prefix nil, :native? false}, org.clojure/test.check {:vsn "1.1.1", :native-prefix nil, :native? false}, metosin/reitit-frontend {:vsn "0.5.18", :native-prefix nil, :native? false}, clj-antlr {:vsn "0.2.12", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-server {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, io.dropwizard.metrics/metrics-jmx {:vsn "4.2.18", :native-prefix nil, :native? false}, org.ow2.asm/asm-analysis {:vsn "6.2", :native-prefix nil, :native? false}, metosin/reitit-spec {:vsn "0.5.18", :native-prefix nil, :native? false}, org.eclipse.jetty.websocket/websocket-common {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}, metosin/jsonista {:vsn "0.3.1", :native-prefix nil, :native? false}, io.pedestal/pedestal.route {:vsn "0.6.0", :native-prefix nil, :native? false}, metosin/reitit-swagger-ui {:vsn "0.5.18", :native-prefix nil, :native? false}, metosin/reitit {:vsn "0.5.18", :native-prefix nil, :native? false}, ring/ring-servlet {:vsn "1.9.6", :native-prefix nil, :native? false}, ch.qos.logback/logback-classic {:vsn "1.4.11", :native-prefix nil, :native? false}, org.clojure/core.memoize {:vsn "1.0.253", :native-prefix nil, :native? false}, com.stuartsierra/dependency {:vsn "1.0.0", :native-prefix nil, :native? false}, tech.droit/clj-diff {:vsn "1.0.1", :native-prefix nil, :native? false}, org.clojure/data.priority-map {:vsn "1.1.0", :native-prefix nil, :native? false}, cider/piggieback {:vsn "0.5.3", :native-prefix nil, :native? false}, org.clojure/java.data {:vsn "1.0.95", :native-prefix nil, :native? false}, org.antlr/antlr4 {:vsn "4.9.3", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-server {:vsn "9.4.48.v20220622", :native-prefix nil, :native? false}, commons-io {:vsn "2.11.0", :native-prefix nil, :native? false}, org.slf4j/log4j-over-slf4j {:vsn "2.0.9", :native-prefix nil, :native? false}, metosin/reitit-dev {:vsn "0.5.18", :native-prefix nil, :native? false}, ring/ring-core {:vsn "1.9.6", :native-prefix nil, :native? false}, org.clojure/core.cache {:vsn "1.0.225", :native-prefix nil, :native? false}, org.ow2.asm/asm-tree {:vsn "6.2", :native-prefix nil, :native? false}, org.clojure/core.async {:vsn "1.6.673", :native-prefix nil, :native? false}, com.fasterxml.jackson.dataformat/jackson-dataformat-smile {:vsn "2.10.2", :native-prefix nil, :native? false}, com.fasterxml.jackson.datatype/jackson-datatype-jsr310 {:vsn "2.12.0", :native-prefix nil, :native? false}, org.glassfish/javax.json {:vsn "1.0.4", :native-prefix nil, :native? false}, javax.xml.bind/jaxb-api {:vsn "2.3.0", :native-prefix nil, :native? false}, metosin/sieppari {:vsn "0.0.0-alpha13", :native-prefix nil, :native? false}, metosin/reitit-interceptors {:vsn "0.5.18", :native-prefix nil, :native? false}, org.eclipse.jetty/jetty-alpn-server {:vsn "9.4.51.v20230217", :native-prefix nil, :native? false}}}]