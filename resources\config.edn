{
  ;; Настройки окружения
  :env (or #env "dev" :dev)  ;; dev, test, prod
  
  ;; Настройки GraphQL сервера
  :graphql {
    :host #or ["HOST" "0.0.0.0"]
    :port #or [#env "PORT" 8080]
    :env #or [#env "ENV" :dev]
    :context ""
    :max-threads 200
    :max-connections 10000
    :max-queue-size 1000
    :request-timeout 30000
    :idle-timeout 120000
    :shutdown-timeout 10000

    ;; Настройки CORS
    :cors {
      :allowed-origins [#"http://localhost:3000" #"http://127.0.0.1:3000"]
      :allowed-methods [:get :post :put :delete :options]
      :allowed-headers ["Content-Type" "Authorization"]
      :allow-credentials true
      :max-age 3600
    }

    ;; Настройки безопасности
    :security {
      :content-security-policy "default-src 'self'"
      :frame-options "SAMEORIGIN"
      :xss-protection "1; mode=block"
      :content-type-options "nosniff"
      :hsts "max-age=31536000; includeSubdomains"
    }

    ;; GraphQL специфичные настройки
    :playground {
      :enabled #profile {:dev true :test false :prod false}
      :endpoint "/ide"
    }
    :introspection {
      :enabled #profile {:dev true :test false :prod false}
    }
  }
  
  ;; Настройки базы данных
  :database {
    :dbtype "h2"
    :dbname "./data/clojure_agent"
    :user "sa"
    :password ""
    :host "localhost"
    :port 0
    :classname "org.h2.Driver"
    :subprotocol "h2"
    :subname "file:./data/clojure_agent"
    :auto-commit true
    :read-only false
    :connection-timeout 30000
    :validation-timeout 5000
    :idle-timeout 600000
    :max-lifetime 1800000
    :minimum-idle 10
    :maximum-pool-size 20
    :pool-name "clojure-agent-db-pool"
    :adapter "h2"
    :migration {
      :store :database
      :migration-table "schema_migrations"
      :init-in-available? true
    }
  }
  
  ;; Настройки агентной системы
  :agent {
    :default-type :worker
    :max-agents 1000
    :shutdown-timeout 30000
    :thread-pool {
      :core-pool-size 4
      :max-pool-size 32
      :keep-alive-time 60000
      :queue-capacity 10000
    }
    :metrics {
      :enabled true
      :report-interval 60000  ;; в миллисекундах
    }
  }
  
  ;; Настройки логирования
  :logging {
    :level #or [#env "LOG_LEVEL" :info]  ;; :trace, :debug, :info, :warn, :error, :fatal, :report
    :console? true
    :file "./logs/clojure-agent.log"
    :max-size "50MB"
    :max-backup 5
    :pattern "%date %-5level [%thread] %logger{36} - %msg%n"
    :appenders {
      :console {:enabled true}
      :file {:enabled true}
      :sentry {:enabled false}
    }
  }
  
  ;; Настройки метрик
  :metrics {
    :enabled true
    :jmx {
      :enabled true
      :domain "clojure.agent"
    }
    :prometheus {
      :enabled true
      :endpoint "/metrics"
    }
    :reporters {
      :console {
        :enabled true
        :rate-units "SECONDS"
        :duration-units "MILLISECONDS"
        :report-interval 60  ;; в секундах
      }
    }
  }
  
  ;; Настройки кэширования
  :cache {
    :default-ttl 300  ;; 5 минут
    :max-size 10000
    :stats-enabled true
    :caches {
      :session {
        :ttl 3600  ;; 1 час
        :max-size 1000
      }
      :api-responses {
        :ttl 60  ;; 1 минута
        :max-size 1000
      }
    }
  }
  
  ;; Настройки безопасности
  :security {
    :auth {
      :enabled true
      :jwt-secret #or [#env "JWT_SECRET" "change-me-in-production"]
      :token-expiration 86400  ;; 24 часа
      :password-salt-rounds 10
    }
    :rate-limiting {
      :enabled true
      :default-limit 100
      :default-window 60  ;; в секундах
      :ip-whitelist ["127.0.0.1"]
    }
  }
  
  ;; Настройки для разработки
  :dev? #profile {:dev true :test false :prod false}
  :test? #profile {:dev false :test true :prod false}
  :prod? #profile {:dev false :test false :prod true}
  
  ;; Внешние сервисы (пример)
  :external-services {
    :storage {
      :type :s3  ;; или :local, :gcs, :azure
      :endpoint "http://localhost:4566"  ;; для localstack
      :region "us-east-1"
      :bucket "clojure-agent"
      :access-key #env ["AWS_ACCESS_KEY_ID" :required? true]
      :secret-key #env ["AWS_SECRET_ACCESS_KEY" :required? true]
    }
  }
}
