(ns clojure-agent.api.routes
  "Маршруты API для работы с агентами."
  (:require [compojure.core :refer [defroutes GET POST PUT DELETE context routes]]
            [compojure.route :as route]
            [ring.util.response :refer [response status]]
            [ring.middleware.params :refer [wrap-params]]
            [ring.middleware.keyword-params :refer [wrap-keyword-params]]
            [ring.middleware.json :refer [wrap-json-body wrap-json-response]]
            [clojure.tools.logging :as log]
            [clojure.string :as str]
            [clojure.walk :as walk]
            [clojure.spec.alpha :as s]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.db.core :as db]
            [clojure-agent.middleware :as mw]
            [clojure-agent.metrics :as metrics]
            [clojure-agent.cache :as cache]))

;; ===========================================================================
;; Валидация входных данных
;; ===========================================================================

(s/def ::non-empty-string (s/and string? (complement str/blank?)))
(s/def ::agent-id ::non-empty-string)
(s/def ::agent-type keyword?)
(s/def ::agent-params map?)
(s/def ::message-type keyword?)
(s/def ::message-payload map?)

(s/def ::create-agent-request
  (s/keys :req-un [::agent-id ::agent-type]
          :opt-un [::agent-params]))

(s/def ::send-message-request
  (s/keys :req-un [::message-type]
          :opt-un [::message-payload]))

(defn validate-request
  "Валидирует запрос по указанной спецификации."
  [spec data]
  (if (s/valid? spec data)
    [true nil]
    [false (s/explain-data spec data)]))

;; ===========================================================================
;; Обработчики ответов
;; ===========================================================================

(defn success
  "Успешный ответ API."
  ([data] (success data 200))
  ([data status]
   {:status status
    :headers {"Content-Type" "application/json; charset=utf-8"}
    :body {:success true
           :data data}}))

(defn error
  "Ошибочный ответ API."
  ([message] (error message 400 nil))
  ([message status] (error message status nil))
  ([message status details]
   (let [response {:status status
                    :headers {"Content-Type" "application/json; charset=utf-8"}
                    :body {:success false
                           :error message}}]
     (when details
       (assoc-in response [:body :details] details))
     response)))

(defn validation-error
  "Ошибка валидации входных данных."
  [explanation]
  (error "Ошибка валидации" 400 {:details (walk/keywordize-keys explanation)}))

;; ===========================================================================
;; Middleware
;; ===========================================================================

(defn wrap-api-version
  "Добавляет заголовок с версией API."
  [handler]
  (fn [request]
    (let [response (handler request)]
      (assoc-in response [:headers "X-API-Version"] "1.0.0"))))

(defn wrap-metrics
  "Добавляет сбор метрик для запросов."
  [handler metric-name]
  (fn [request]
    (let [timer (metrics/timer "http" [metric-name "duration"])
          counter (metrics/meter "http" [metric-name "count"])
          start-time (System/nanoTime)]
      (metrics/mark-meter! counter)
      (let [response (handler request)
            duration (- (System/nanoTime) start-time)]
        (metrics/record-time! timer duration)
        (assoc response :duration-ms (/ duration 1000000.0))))))

;; ===========================================================================
;; Обработчики маршрутов
;; ===========================================================================

(defn create-agent-handler [db agent-system]
  "Создает нового агента."
  [request]
  (let [body (:body request)
        [valid? validation-result] (validate-request ::create-agent-request body)]
    (if valid?
      (try
        (let [{:keys [id type params]} body
              agent (agent/create-agent agent-system id type (or params {}))]
          (success agent 201))
        (catch clojure.lang.ExceptionInfo e
          (let [data (ex-data e)]
            (cond
              (= :agent-already-exists (:type data))
              (error "Агент с указанным ID уже существует" 409)
              
              :else
              (do
                (log/error e "Ошибка создания агента" id)
                (error "Не удалось создать агента" 500 (ex-data e))))))
        (catch Exception e
          (log/error e "Неизвестная ошибка при создании агента" id)
          (error "Внутренняя ошибка сервера" 500)))
      (validation-error validation-result))))

(defn get-agent-handler [agent-system]
  "Возвращает информацию об агенте по ID."
  [request]
  (let [id (get-in request [:params :id])]
    (if-let [agent (agent/get-agent agent-system id)]
      (success agent)
      (error (str "Агент с ID " id " не найден") 404))))

(defn send-message-handler [agent-system]
  "Отправляет сообщение агенту."
  [request]
  (let [agent-id (get-in request [:params :id])
        message (:body request)
        [valid? validation-result] (validate-request ::send-message-request message)]
    (if valid?
      (try
        (let [result (agent/send-message agent-system agent-id message)]
          (success result))
        (catch clojure.lang.ExceptionInfo e
          (let [data (ex-data e)]
            (cond
              (= :agent-not-found (:type data))
              (error (str "Агент с ID " agent-id " не найден") 404)
              
              (= :message-handler-not-found (:type data))
              (error "Обработчик сообщений для данного типа агента не найден" 400)
              
              :else
              (do
                (log/error e "Ошибка отправки сообщения агенту" agent-id)
                (error "Ошибка обработки сообщения" 500 (ex-data e))))))
        (catch Exception e
          (log/error e "Неизвестная ошибка при отправке сообщения агенту" agent-id)
          (error "Внутренняя ошибка сервера" 500)))
      (validation-error validation-result))))

(defn list-agents-handler [agent-system]
  "Возвращает список всех зарегистрированных агентов."
  [_]
  (success (agent/list-agents agent-system)))

(defn health-check-handler
  "Проверка работоспособности сервиса."
  [_]
  (success {:status "ok" :timestamp (System/currentTimeMillis)}))

(defn metrics-handler
  "Возвращает метрики приложения."
  [_]
  (success (metrics/get-all-metrics)))

;; ===========================================================================
;; Определение маршрутов
;; ===========================================================================

(defn api-routes [db agent-system]
  "Определение маршрутов API."
  (routes
    (context "/api/v1" []
      (-> (routes
            ;; Агенты
            (POST "/agents" [] (create-agent-handler db agent-system))
            (GET "/agents" [] (list-agents-handler agent-system))
            (GET "/agents/:id" [id] (get-agent-handler agent-system))
            (POST "/agents/:id/messages" [id] (send-message-handler agent-system))
            
            ;; Системные
            (GET "/health" [] health-check-handler)
            (GET "/metrics" [] metrics-handler)
            
            ;; Обработка 404
            (route/not-found (error "Ресурс не найден" 404)))
          
          ;; Общие middleware
          (wrap-json-body {:keywords? true :bigdecimals? true})
          wrap-json-response
          wrap-keyword-params
          wrap-params
          (wrap-metrics "api")
          wrap-api-version))))

(defn app-routes [db agent-system]
  "Основные маршруты приложения."
  (routes
    (api-routes db agent-system)
    
    ;; Обработка 404 для всех остальных маршрутов
    (route/not-found 
      (error "Маршрут не найден. Используйте /api/v1/ для доступа к API" 404))))
