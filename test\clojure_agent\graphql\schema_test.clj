(ns clojure-agent.graphql.schema-test
  "Тесты для GraphQL схемы"
  (:require [clojure.test :refer :all]
            [com.walmartlabs.lacinia :as lacinia]
            [clojure-agent.test-helper :refer :all]
            [clojure-agent.graphql.schema :as schema]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.system :as system]))

(use-fixtures :once with-test-system)
(use-fixtures :each with-clean-db with-clean-cache)

(defn execute-query
  "Выполняет GraphQL запрос с тестовой системой"
  [query & [variables]]
  (with-system [sys (system/start (system/new-system test-config))]
    (let [compiled-schema (schema/create-schema)
          context {:system {:db (:db sys)
                           :agent (:agent sys)
                           :cache (:cache sys)
                           :metrics (:metrics sys)}}]
      (lacinia/execute compiled-schema query variables context))))

(deftest test-health-query
  (testing "Запрос health должен возвращать статус системы"
    (let [query "query { health { status timestamp version } }"
          result (execute-query query)]
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (= "ok" (get-in result [:data :health :status])) "Статус должен быть ok")
      (is (string? (get-in result [:data :health :timestamp])) "Timestamp должен быть строкой")
      (is (= "1.0.0" (get-in result [:data :health :version])) "Версия должна быть 1.0.0"))))

(deftest test-metrics-query
  (testing "Запрос metrics должен возвращать метрики системы"
    (let [query "query { metrics { agents_count messages_processed memory_usage cpu_usage } }"
          result (execute-query query)]
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (number? (get-in result [:data :metrics :agents_count])) "agents_count должен быть числом")
      (is (number? (get-in result [:data :metrics :messages_processed])) "messages_processed должен быть числом")
      (is (number? (get-in result [:data :metrics :memory_usage])) "memory_usage должен быть числом")
      (is (number? (get-in result [:data :metrics :cpu_usage])) "cpu_usage должен быть числом"))))

(deftest test-create-agent-mutation
  (testing "Мутация createAgent должна создавать нового агента"
    (let [mutation "mutation CreateAgent($input: CreateAgentInput!) {
                     createAgent(input: $input) {
                       id
                       type
                       state
                       created_at
                       updated_at
                     }
                   }"
          variables {:input {:id "test-agent-1"
                            :type "worker"
                            :initial_state {:counter 0}}}
          result (execute-query mutation variables)]
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (= "test-agent-1" (get-in result [:data :createAgent :id])) "ID агента должен совпадать")
      (is (= "worker" (get-in result [:data :createAgent :type])) "Тип агента должен совпадать")
      (is (= {:counter 0} (get-in result [:data :createAgent :state])) "Состояние агента должно совпадать"))))

(deftest test-list-agents-query
  (testing "Запрос agents должен возвращать список агентов"
    ;; Сначала создаем агента
    (let [create-mutation "mutation CreateAgent($input: CreateAgentInput!) {
                            createAgent(input: $input) { id }
                          }"
          create-variables {:input {:id "test-agent-2"
                                   :type "worker"
                                   :initial_state {:status "active"}}}
          _ (execute-query create-mutation create-variables)
          
          ;; Затем запрашиваем список
          list-query "query { agents { id type state } }"
          result (execute-query list-query)]
      
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (vector? (get-in result [:data :agents])) "agents должен быть массивом")
      (is (some #(= "test-agent-2" (:id %)) (get-in result [:data :agents])) 
          "Созданный агент должен быть в списке"))))

(deftest test-get-agent-query
  (testing "Запрос agent должен возвращать конкретного агента"
    ;; Сначала создаем агента
    (let [create-mutation "mutation CreateAgent($input: CreateAgentInput!) {
                            createAgent(input: $input) { id }
                          }"
          create-variables {:input {:id "test-agent-3"
                                   :type "processor"
                                   :initial_state {:data "test"}}}
          _ (execute-query create-mutation create-variables)
          
          ;; Затем запрашиваем его
          get-query "query GetAgent($id: String!) {
                      agent(id: $id) {
                        id
                        type
                        state
                      }
                    }"
          get-variables {:id "test-agent-3"}
          result (execute-query get-query get-variables)]
      
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (= "test-agent-3" (get-in result [:data :agent :id])) "ID агента должен совпадать")
      (is (= "processor" (get-in result [:data :agent :type])) "Тип агента должен совпадать")
      (is (= {:data "test"} (get-in result [:data :agent :state])) "Состояние агента должно совпадать"))))

(deftest test-send-message-mutation
  (testing "Мутация sendMessage должна отправлять сообщение агенту"
    ;; Сначала создаем агента
    (let [create-mutation "mutation CreateAgent($input: CreateAgentInput!) {
                            createAgent(input: $input) { id }
                          }"
          create-variables {:input {:id "test-agent-4"
                                   :type "worker"
                                   :initial_state {:counter 0}}}
          _ (execute-query create-mutation create-variables)
          
          ;; Затем отправляем сообщение
          send-mutation "mutation SendMessage($input: SendMessageInput!) {
                          sendMessage(input: $input) {
                            success
                            result
                            error
                            agent_state
                          }
                        }"
          send-variables {:input {:agent_id "test-agent-4"
                                 :message_type "update"
                                 :payload {:counter 1}}}
          result (execute-query send-mutation send-variables)]
      
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (true? (get-in result [:data :sendMessage :success])) "Отправка должна быть успешной")
      (is (nil? (get-in result [:data :sendMessage :error])) "Не должно быть ошибки")
      (is (map? (get-in result [:data :sendMessage :agent_state])) "agent_state должен быть объектом"))))

(deftest test-nonexistent-agent-query
  (testing "Запрос несуществующего агента должен возвращать null"
    (let [query "query GetAgent($id: String!) {
                  agent(id: $id) {
                    id
                    type
                    state
                  }
                }"
          variables {:id "nonexistent-agent"}
          result (execute-query query variables)]
      
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (nil? (get-in result [:data :agent])) "Агент должен быть null"))))

(deftest test-invalid-mutation-input
  (testing "Мутация с невалидными данными должна возвращать ошибку"
    (let [mutation "mutation CreateAgent($input: CreateAgentInput!) {
                     createAgent(input: $input) {
                       id
                       type
                     }
                   }"
          variables {:input {:id ""  ;; Пустой ID
                            :type "worker"}}
          result (execute-query mutation variables)]
      
      (is (seq (:errors result)) "Должны быть ошибки валидации"))))

(deftest test-schema-compilation
  (testing "GraphQL схема должна компилироваться без ошибок"
    (is (some? (schema/create-schema)) "Схема должна быть скомпилирована")))

(deftest test-schema-validation
  (testing "GraphQL схема должна быть валидной"
    (is (true? (schema/validate-schema)) "Схема должна быть валидной")))

(deftest test-introspection-query
  (testing "Запрос интроспекции должен работать"
    (let [query "query IntrospectionQuery {
                  __schema {
                    queryType { name }
                    mutationType { name }
                    types {
                      name
                      kind
                    }
                  }
                }"
          result (execute-query query)]
      
      (is (nil? (:errors result)) "Не должно быть ошибок")
      (is (= "QueryRoot" (get-in result [:data :__schema :queryType :name])) 
          "QueryType должен быть QueryRoot")
      (is (= "MutationRoot" (get-in result [:data :__schema :mutationType :name])) 
          "MutationType должен быть MutationRoot")
      (is (vector? (get-in result [:data :__schema :types])) 
          "types должен быть массивом"))))

(deftest test-concurrent-operations
  (testing "Параллельные операции должны работать корректно"
    (let [create-mutation "mutation CreateAgent($input: CreateAgentInput!) {
                            createAgent(input: $input) { id }
                          }"
          futures (doall
                   (for [i (range 5)]
                     (future
                       (execute-query create-mutation
                                     {:input {:id (str "concurrent-agent-" i)
                                             :type "worker"
                                             :initial_state {:index i}}}))))
          results (map deref futures)]
      
      (is (every? #(nil? (:errors %)) results) "Все операции должны быть успешными")
      (is (= 5 (count results)) "Должно быть 5 результатов"))))

(deftest test-error-handling
  (testing "Обработка ошибок в резолверах"
    (let [mutation "mutation SendMessage($input: SendMessageInput!) {
                     sendMessage(input: $input) {
                       success
                       error
                     }
                   }"
          variables {:input {:agent_id "nonexistent-agent"
                            :message_type "test"
                            :payload {}}}
          result (execute-query mutation variables)]
      
      (is (nil? (:errors result)) "Не должно быть GraphQL ошибок")
      (is (false? (get-in result [:data :sendMessage :success])) "Операция должна быть неуспешной")
      (is (string? (get-in result [:data :sendMessage :error])) "Должно быть сообщение об ошибке"))))
