(ns clojure-agent.config
  "Управление конфигурацией приложения."
  (:require [aero.core :as aero]
            [clojure.java.io :as io]
            [clojure.string :as str]
            [taoensso.timbre :as log]

            [clojure.edn :as edn]
            [clojure.walk :as walk]))

(defn- read-config-file
  "Читает конфигурационный файл с поддержкой профилей Aero."
  [resource-path]
  (try
    (-> (io/resource resource-path)
        (aero/read-config {:resolver aero/resource-resolver
                          :profile (keyword (or (System/getenv "CLJ_ENV") "dev"))}))
    (catch Exception e
      (log/error e "Ошибка чтения конфигурационного файла:" resource-path)
      (throw (ex-info (str "Не удалось загрузить конфигурацию из " resource-path)
                      {:resource resource-path}
                      e)))))

(defn- deep-merge
  "Рекурсивно объединяет карты."
  [& maps]
  (apply merge-with
         (fn [x y]
           (cond (and (map? x) (map? y)) (deep-merge x y)
                 (vector? x) (into x y)
                 :else y))
         maps))

(defn- resolve-env-vars
  "Заменяет строки вида ${ENV_VAR} на значения переменных окружения."
  [config]
  (let [pattern #"\$\{([^}]+)\}"
        resolve-fn (fn [[_ var-name]] (or (System/getenv var-name) ""))]
    (walk/postwalk
     (fn [x]
       (cond
         (string? x) (str/replace x pattern resolve-fn)
         :else x))
     config)))

(defn- coerce-types
  "Приводит типы значений в конфигурации к ожидаемым."
  [config]
  (walk/postwalk
   (fn [x]
     (cond
       (and (map? x) (contains? x :coerce/boolean))
       (let [v (x :coerce/boolean)]
         (cond
           (string? v) (Boolean/parseBoolean v)
           (boolean? v) v
           :else (throw (ex-info (str "Неверное булево значение: " (pr-str v)) {:value v}))))
       
       (and (map? x) (contains? x :coerce/int))
       (let [v (x :coerce/int)]
         (if (string? v)
           (try
             (Integer/parseInt v)
             (catch NumberFormatException e
               (log/error e "Ошибка преобразования в число:" v)
               (throw (ex-info (str "Неверное числовое значение: " v) {:value v} e))))
           v))
       
       (and (map? x) (contains? x :coerce/keyword))
       (let [v (x :coerce/keyword)]
         (if (string? v)
           (keyword v)
           v))
       
       :else x))
   config))

(defn- apply-overrides
  "Применяет переопределения из переменных окружения к конфигурации."
  [config prefix]
  (let [prefix (str (when prefix (str (str/upper-case (name prefix)) "_")))]
    (reduce-kv
     (fn [m k v]
       (if (map? v)
         (assoc m k (apply-overrides v (str prefix (name k) "_")))
         (let [env-var (-> (name k)
                          (str/replace "-" "_")
                          (str/upper-case)
                          (str prefix)
                          (System/getenv))]
           (if env-var
             (do
               (log/debug "Переопределение конфига из переменной окружения:" (str prefix (name k)) "=" env-var)
               (assoc m k (if (string? v) env-var (read-string env-var))))
             m))))
     {}
     config)))

(defn load-config
  "Загружает конфигурацию из файла и применяет переопределения из переменных окружения.
  Поддерживает профили Aero и вложенную конфигурацию."
  ([] (load-config "config.edn"))
  ([config-file]
   (try
     (let [config (-> (read-config-file config-file)
                     (resolve-env-vars)
                     (coerce-types)
                     (apply-overrides nil))]
       (log/info "Конфигурация успешно загружена")
       config)
     (catch Exception e
       (log/error e "Критическая ошибка загрузки конфигурации")
       (throw (ex-info "Не удалось загрузить конфигурацию"
                       {:config-file config-file}
                       e))))))

(defn get-in-config
  "Безопасно получает значение из конфигурации по пути."
  ([config path]
   (get-in config path))
  ([config path not-found]
   (get-in config path not-found)))

(defn env
  "Получает значение переменной окружения или значение по умолчанию."
  ([k] (env k nil))
  ([k default]
   (or (System/getenv (name k)) default)))

(defn env-int
  "Получает целочисленное значение переменной окружения."
  ([k] (env-int k nil))
  ([k default]
   (if-let [v (env k)]
     (try
       (Integer/parseInt v)
       (catch NumberFormatException _
         (log/warn "Некорректное целочисленное значение для" k ":" v)
         default))
     default)))

(defn env-bool
  "Получает булево значение переменной окружения."
  ([k] (env-bool k nil))
  ([k default]
   (if-let [v (env k)]
     (cond
       (re-matches #"(?i)true|yes|on|1" v) true
       (re-matches #"(?i)false|no|off|0" v) false
       :else (do
               (log/warn "Некорректное булево значение для" k ":" v)
               default))
     default)))
