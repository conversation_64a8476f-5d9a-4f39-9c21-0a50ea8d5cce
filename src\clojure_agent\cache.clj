(ns clojure-agent.cache
  "Модуль для кэширования данных в памяти."
  (:require [com.stuartsierra.component :as component]
            [clojure.tools.logging :as log]
            [clojure.core.cache :as cache]
            [clojure.string :as str]
            [clojure.set :as set]))

;; ===========================================================================
;; Внутренние утилиты
;; ===========================================================================

(defn- normalize-cache-name
  "Нормализует имя кэша."
  [name]
  (if (keyword? name)
    (name name)
    (str name)))

(defn- create-cache-instance
  "Создает экземпляр кэша на основе конфигурации."
  [{:keys [type ttl max-size] :or {type :basic ttl 300 max-size 1000}}]
  (log/debug "Создание кэша типа" type "с TTL" ttl "мс и максимальным размером" max-size)
  (case type
    :basic (cache/basic-cache-factory {})
    :lru (cache/lru-cache-factory {} :threshold max-size)
    (cache/basic-cache-factory {})))

;; ===========================================================================
;; Компонент системы кэширования
;; ===========================================================================

(defrecord CacheSystem [config caches]
  component/Lifecycle
  (start [this]
    (log/info "Запуск системы кэширования...")
    (let [default-config (merge
                           {:type :ttl :ttl 300 :max-size 1000}
                           (get config :default {}))
          
          ;; Инициализируем предопределенные кэши
          caches (reduce
                   (fn [m [name cache-config]]
                     (let [merged-config (merge default-config cache-config)]
                       (assoc m (normalize-cache-name name)
                                (atom (create-cache-instance merged-config)))))
                   {}
                   (get config :caches {}))]
      
      (log/info "Система кэширования запущена")
      (assoc this :caches caches)))
  
  (stop [this]
    (log/info "Остановка системы кэширования...")
    (assoc this :caches {}))
  
  ;; Протокол для работы с кэшем
  clojure.lang.ILookup
  (valAt [this k] (.valAt this k nil))
  (valAt [this k not-found]
    (get caches (normalize-cache-name k) not-found)))

;; ===========================================================================
;; Публичный API
;; ===========================================================================

(defn new-cache-system
  "Создает новую систему кэширования с указанной конфигурацией."
  [config]
  (map->CacheSystem {:config config :caches {}}))

;; ===========================================================================
;; Удобные функции для работы с кэшем
;; ===========================================================================

(defn get-cache
  "Получает кэш по имени. Если кэш не существует, создает его с настройками по умолчанию."
  ([cache-system name] (get-cache cache-system name nil))
  ([{:keys [caches config] :as cache-system} name default-config]
   (let [cache-key (normalize-cache-name name)]
     (or (get caches cache-key)
         (when-let [new-cache (when default-config
                                (create-cache-instance
                                  (merge (get config :default {}) default-config)))]
           (log/debug "Создан новый кэш с настройками по умолчанию:" cache-key)
           (swap! (::cache-atom cache-system) assoc cache-key new-cache)
           new-cache)
         (throw (ex-info (str "Кэш не найден: " name) {:name name}))))))

(defn get-value
  "Получает значение из кэша по ключу."
  [cache key]
  (cache/lookup cache key))

(defn put-value!
  "Добавляет значение в кэш."
  [cache key value]
  (cache/miss cache key value))

(defn evict-value!
  "Удаляет значение из кэша по ключу."
  [cache key]
  (cache/evict cache key))

(defn clear-cache!
  "Очищает весь кэш."
  [cache]
  (cache/seed cache {}))

(defn cache-stats
  "Возвращает статистику использования кэша."
  [cache]
  (when-let [stats-fn (get-method cache/-stats (type cache))]
    (stats-fn cache)))

;; ===========================================================================
;; Макросы для удобной работы с кэшированием
;; ===========================================================================

(defmacro with-cache
  "Выполняет вычисление и кэширует результат. Если значение уже есть в кэше,
  возвращает его, иначе вычисляет и сохраняет в кэш."
  [cache key & body]
  `(if-let [cached# (get-value ~cache ~key)]
     (do
       (log/trace "Кэш-попадание для ключа:" ~key)
       cached#)
     (let [result# (do ~@body)]
       (log/trace "Кэш-промах для ключа:" ~key)
       (put-value! ~cache ~key result#)
       result#)))

(defmacro defcached
  "Определяет функцию с кэшированием результатов."
  [name docstring cache-var args & body]
  (let [cache-sym (gensym "cache-")]
    `(let [~cache-sym ~cache-var]
       (defn ~name ~docstring ~args
         (with-cache ~cache-sym (hash ~args)
           (do ~@body))))))

;; ===========================================================================
;; Расширенные стратегии кэширования
;; ===========================================================================

(defn memoize-cached
  "Возвращает мемоизированную версию функции с кэшированием результатов."
  ([f cache] (memoize-cached f cache hash))
  ([f cache key-fn]
   (let [cache-atom (atom cache)]
     (fn [& args]
       (let [cache-key (key-fn args)]
         (if-let [cached (get-value @cache-atom cache-key)]
           cached
           (let [result (apply f args)]
             (swap! cache-atom #(put-value! % cache-key result))
             result)))))))

(defn ttl-memoize
  "Возвращает мемоизированную версию функции с простым кэшированием."
  [f ttl-ms]
  (let [cache (atom (cache/basic-cache-factory {}))]
    (fn [& args]
      (let [cache-key (hash args)]
        (if-let [cached (cache/lookup @cache cache-key)]
          cached
          (let [result (apply f args)]
            (swap! cache #(cache/miss % cache-key result))
            result))))))

;; ===========================================================================
;; Интеграция с системой метрик
;; ===========================================================================

(defn wrap-cache-metrics
  "Оборачивает кэш для сбора метрик."
  [cache metrics prefix]
  (let [hits (atom 0)
        misses (atom 0)
        evictions (atom 0)]
    
    (reify
      clojure.core.cache/CacheProtocol
      (lookup [this key]
        (if-let [value (get-value cache key)]
          (do
            (swap! hits inc)
            value)
          (do
            (swap! misses inc)
            nil)))
      
      (has? [_ key] (cache/has? cache key))
      (hit [this key] (wrap-cache-metrics (cache/hit cache key) metrics prefix))
      (miss [this key value] (wrap-cache-metrics (cache/miss cache key value) metrics prefix))
      (evict [this key]
        (swap! evictions inc)
        (wrap-cache-metrics (cache/evict cache key) metrics prefix))
      (seed [this base] (wrap-cache-metrics (cache/seed cache base) metrics prefix))
      
      ;; Для совместимости с wrapped-cache
      clojure.lang.ILookup
      (valAt [this k] (.valAt this k nil))
      (valAt [this k not-found]
        (if-let [v (get {
                        :hits @hits
                        :misses @misses
                        :evictions @evictions
                        :hit-rate (let [total (+ @hits @misses)]
                                   (if (pos? total) (float (/ @hits total)) 0.0))} k)]
          v
          not-found)))))

  ;; Регистрируем метрики
  (when metrics
    (metrics/register-gauge metrics (conj prefix "hits") #(deref hits))
    (metrics/register-gauge metrics (conj prefix "misses") #(deref misses))
    (metrics/register-gauge metrics (conj prefix "evictions") #(deref evictions))
    (metrics/register-gauge metrics (conj prefix "hit-rate")
                           #(let [h @hits m @misses
                                 total (+ h m)]
                              (if (pos? total) (float (/ h total)) 0.0))))
  
  (wrap-cache-metrics cache nil prefix))
