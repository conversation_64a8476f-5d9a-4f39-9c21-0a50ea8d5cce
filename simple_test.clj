(ns simple-test
  (:require [clj-http.client :as http]
            [cheshire.core :as json]))

(defn test-graphql-query [query]
  (try
    (let [response (http/post "http://localhost:8080/api"
                              {:headers {"Content-Type" "application/json"}
                               :body (json/generate-string {:query query})
                               :throw-exceptions false})]
      (println "Status:" (:status response))
      (println "Response:" (json/parse-string (:body response) true)))
    (catch Exception e
      (println "Error:" (.getMessage e)))))

(println "Testing GraphQL API...")
(println "\n1. Health check:")
(test-graphql-query "{ health { status timestamp } }")

(println "\n2. Metrics:")
(test-graphql-query "{ metrics { uptime requestCount } }")

(println "\n3. Agents list:")
(test-graphql-query "{ agents { id type status } }")

(println "\nDone!")
