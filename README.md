# XSQUARE Agent

Персональный AI-агент с веб-интерфейсом чата, оркестратором Pel и интеграцией с LLM.

## 🚀 Быстрый старт

### Установка и запуск

```bash
cd agent/
pip install -r requirements.txt
python main.py
```

Откройте браузер и перейдите на http://localhost:8000

## 🎯 Основные возможности

### 💬 Веб-чат интерфейс
- Современный UI в стиле ChatGPT с тёмной темой
- Реальное время через WebSocket соединение
- История чатов (сохраняется локально)
- Адаптивный дизайн для мобильных устройств
- Экспорт диалогов в JSON

### 🤖 AI-ассистент
- Интеграция с OpenRouter API для LLM
- Автоматическое создание и управление задачами
- Графовая память для контекста
- Поддержка различных типов запросов

### 🎼 Pel-оркестратор
- Homoiconic DSL для цепочек агентов
- HTTP API `/pel/execute` для удалённого выполнения
- Параллельное выполнение, условия, циклы, retry
- Встроенная регистрация агентов

### 📊 Система слайсов
- **Chat**: WebSocket чат + история сообщений
- **Tasks**: Управление задачами с NLP-распознаванием
- **Analysis**: LLM-генерация + графовая память
- **Pel**: Оркестрация агентов через DSL

## 🌐 API Endpoints

| Endpoint | Метод | Описание |
|----------|-------|----------|
| `/` | GET | Редирект на чат |
| `/chat` | GET | Веб-интерфейс чата |
| `/chat/ws/{user_id}` | WebSocket | Реальное время чата |
| `/chat/history/{dialog_id}` | GET | История диалога |
| `/pel/execute` | POST | Выполнение Pel-скриптов |
| `/tasks/*` | CRUD | Управление задачами |

## 🛠️ Архитектура

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web UI        │◄──►│   FastAPI        │◄──►│   XSQUARE       │
│   (React-like)  │    │   (WebSocket)    │    │   (External)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                       ┌────────┼────────┐
                       │        │        │
                   ┌───▼───┐ ┌──▼──┐ ┌───▼────┐
                   │ Chat  │ │Tasks│ │  Pel   │
                   │Slice  │ │Slice│ │ Slice  │
                   └───────┘ └─────┘ └────────┘
                                │
                       ┌────────┼────────┐
                       │        │        │
                   ┌───▼───┐ ┌──▼──┐ ┌───▼────┐
                   │  LLM  │ │Graph│ │Registry│
                   │(OpenR)│ │ Mem │ │Agents  │
                   └───────┘ └─────┘ └────────┘
```

## 📝 Конфигурация

Создайте файл `.env`:

```env
# OpenRouter API
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_MODEL=anthropic/claude-3-haiku
OPENROUTER_REFERER=http://localhost:8000
OPENROUTER_TITLE=XSQUARE Agent

# XSQUARE Backend
XSQUARE_URL=http://localhost:3000
XSQUARE_API_KEY=your_xsquare_key
```

## 🎨 Использование чата

1. Откройте http://localhost:8000
2. Начните диалог с AI-ассистентом
3. Создавайте задачи: "Напомни купить молоко завтра"
4. Просматривайте задачи: "Покажи мои задачи"
5. Задавайте любые вопросы для LLM

## 🔧 Pel-оркестратор

Выполняйте сложные сценарии через API:

```bash
curl -X POST http://localhost:8000/pel/execute \
  -H "Content-Type: application/json" \
  -d '{
    "source": "(parallel (agent \"task_create\" :text \"Встреча в 15:00\") (agent \"llm_summarize\" :text \"Длинный текст\"))",
    "vars": {}
  }'
```

## 📚 Документация

- [Pel DSL Guide](XSQUARE%20агент/docs/pel/README.md) - Полное руководство по языку
- [API Reference](XSQUARE%20агент/docs/pel/api.md) - Справочник по API
- [Examples](XSQUARE%20агент/docs/pel/examples.md) - Примеры интеграции

## 🚀 Развёртывание

### Docker
```bash
docker-compose up -d
```

### Production
- Настройте reverse proxy (nginx)
- Используйте HTTPS для WebSocket
- Добавьте аутентификацию и rate-limiting
- Настройте мониторинг и логирование

## 🔒 Безопасность

⚠️ **Внимание**: Текущая версия предназначена для разработки. Для продакшена:

- Добавьте JWT-аутентификацию
- Настройте CORS политики
- Ограничьте eval в Pel-парсере
- Настройте rate-limiting для API

## 🤝 Вклад в проект

1. Fork репозитория
2. Создайте feature branch
3. Commit изменения
4. Push в branch
5. Создайте Pull Request

## 📄 Лицензия

MIT License - см. [LICENSE](LICENSE) файл. 