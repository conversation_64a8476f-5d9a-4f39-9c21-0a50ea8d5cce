# GraphQL API Reference

## Обзор

Clojure Agent System предоставляет современный GraphQL API для взаимодействия с системой агентов. API поддерживает интроспекцию, валидацию типов и предоставляет единую точку входа для всех операций.

## Эндпоинты

- **GraphQL API**: `http://localhost:3000/graphql`
- **GraphQL Playground**: `http://localhost:3000/graphql` (GET запрос в браузере)
- **Health Check**: `http://localhost:3000/health`

## Схема данных

### Основные типы

#### Agent
Представляет агента в системе.

```graphql
type Agent {
  id: String!           # Уникальный идентификатор агента
  type: String!         # Тип агента (worker, manager, analyzer, etc.)
  state: JSON          # Текущее состояние агента в формате JSON
  created_at: String   # Время создания (ISO 8601)
  updated_at: String   # Время последнего обновления (ISO 8601)
}
```

#### CreateAgentInput
Входные данные для создания агента.

```graphql
input CreateAgentInput {
  id: String!           # Уникальный идентификатор
  type: String!         # Тип агента
  initial_state: String # Начальное состояние в формате JSON
}
```

#### SendMessageInput
Входные данные для отправки сообщения агенту.

```graphql
input SendMessageInput {
  agent_id: String!     # ID целевого агента
  message_type: String! # Тип сообщения
  payload: String!      # Полезная нагрузка в формате JSON
}
```

#### MessageResult
Результат отправки сообщения.

```graphql
type MessageResult {
  success: Boolean!     # Успешность операции
  result: String       # Результат обработки
  error: String        # Сообщение об ошибке (если есть)
  agent_state: JSON    # Новое состояние агента
}
```

#### VectorSearchResult
Результат векторного поиска.

```graphql
type VectorSearchResult {
  key: String!         # Ключ найденного элемента
  similarity: Float    # Коэффициент сходства (0.0 - 1.0)
  metadata: JSON       # Метаданные элемента
}
```

#### VectorStats
Статистика векторной базы данных.

```graphql
type VectorStats {
  total_vectors: Int        # Общее количество векторов
  average_vector_size: Float # Средний размер вектора
  memory_usage: String      # Использование памяти
}
```

#### HealthStatus
Статус здоровья системы.

```graphql
type HealthStatus {
  status: String!      # Общий статус (healthy/unhealthy)
  timestamp: String!   # Время проверки
  uptime: String!      # Время работы системы
  version: String!     # Версия системы
}
```

#### SystemMetrics
Метрики системы.

```graphql
type SystemMetrics {
  agents_count: Int        # Количество агентов
  messages_processed: Int  # Обработанных сообщений
  memory_usage: String     # Использование памяти
  cpu_usage: Float        # Использование CPU (%)
}
```

## Запросы (Queries)

### agents
Получение списка всех агентов.

```graphql
query GetAllAgents {
  agents {
    id
    type
    state
    created_at
    updated_at
  }
}
```

**Возвращает**: `[Agent]` - массив всех агентов в системе

### agent(id: String!)
Получение конкретного агента по ID.

```graphql
query GetAgent($id: String!) {
  agent(id: $id) {
    id
    type
    state
    created_at
    updated_at
  }
}
```

**Параметры**:
- `id` (String!) - Уникальный идентификатор агента

**Возвращает**: `Agent` - данные агента или null если не найден

### vectorSearch(query: String!, k: Int)
Семантический поиск в векторной базе данных.

```graphql
query SearchSimilarAgents($query: String!, $k: Int) {
  vectorSearch(query: $query, k: $k) {
    key
    similarity
    metadata
  }
}
```

**Параметры**:
- `query` (String!) - Поисковый запрос
- `k` (Int) - Количество результатов (по умолчанию 10)

**Возвращает**: `[VectorSearchResult]` - результаты поиска, отсортированные по убыванию сходства

### vectorStats
Статистика векторной базы данных.

```graphql
query GetVectorStats {
  vectorStats {
    total_vectors
    average_vector_size
    memory_usage
  }
}
```

**Возвращает**: `VectorStats` - статистика использования векторной БД

### health
Проверка здоровья системы.

```graphql
query HealthCheck {
  health {
    status
    timestamp
    uptime
    version
  }
}
```

**Возвращает**: `HealthStatus` - текущий статус системы

### metrics
Метрики производительности системы.

```graphql
query GetMetrics {
  metrics {
    agents_count
    messages_processed
    memory_usage
    cpu_usage
  }
}
```

**Возвращает**: `SystemMetrics` - текущие метрики системы

## Мутации (Mutations)

### createAgent(input: CreateAgentInput!)
Создание нового агента.

```graphql
mutation CreateAgent($input: CreateAgentInput!) {
  createAgent(input: $input) {
    id
    type
    state
    created_at
    updated_at
  }
}
```

**Параметры**:
- `input` (CreateAgentInput!) - Данные для создания агента

**Пример переменных**:
```json
{
  "input": {
    "id": "agent-001",
    "type": "worker",
    "initial_state": "{\"status\": \"active\", \"task_queue\": []}"
  }
}
```

**Возвращает**: `Agent` - созданный агент

**Ошибки**:
- Агент с таким ID уже существует
- Некорректный формат initial_state

### sendMessage(input: SendMessageInput!)
Отправка сообщения агенту.

```graphql
mutation SendMessage($input: SendMessageInput!) {
  sendMessage(input: $input) {
    success
    result
    error
    agent_state
  }
}
```

**Параметры**:
- `input` (SendMessageInput!) - Данные сообщения

**Пример переменных**:
```json
{
  "input": {
    "agent_id": "agent-001",
    "message_type": "task",
    "payload": "{\"action\": \"process_data\", \"data\": [1,2,3]}"
  }
}
```

**Возвращает**: `MessageResult` - результат обработки сообщения

**Ошибки**:
- Агент не найден
- Некорректный формат payload
- Ошибка обработки сообщения

## Примеры использования

### Создание и настройка агента

```graphql
# 1. Создание агента
mutation {
  createAgent(input: {
    id: "data-processor-01"
    type: "processor"
    initial_state: "{\"status\": \"idle\", \"processed_items\": 0}"
  }) {
    id
    type
    state
  }
}

# 2. Отправка задачи агенту
mutation {
  sendMessage(input: {
    agent_id: "data-processor-01"
    message_type: "process_task"
    payload: "{\"task_id\": \"task-123\", \"data\": [1,2,3,4,5]}"
  }) {
    success
    result
    agent_state
  }
}

# 3. Проверка состояния агента
query {
  agent(id: "data-processor-01") {
    id
    state
    updated_at
  }
}
```

### Семантический поиск агентов

```graphql
# Поиск агентов по описанию
query {
  vectorSearch(query: "data processing worker", k: 5) {
    key
    similarity
    metadata
  }
}

# Получение статистики поиска
query {
  vectorStats {
    total_vectors
    average_vector_size
    memory_usage
  }
}
```

### Мониторинг системы

```graphql
# Проверка здоровья
query {
  health {
    status
    uptime
    version
  }
}

# Получение метрик
query {
  metrics {
    agents_count
    messages_processed
    memory_usage
    cpu_usage
  }
}

# Список всех агентов
query {
  agents {
    id
    type
    updated_at
  }
}
```

## Обработка ошибок

GraphQL API возвращает ошибки в стандартном формате:

```json
{
  "errors": [
    {
      "message": "Agent with id 'non-existent' not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["agent"]
    }
  ],
  "data": {
    "agent": null
  }
}
```

### Типы ошибок

- **Validation errors**: Ошибки валидации входных данных
- **Not found errors**: Запрашиваемый ресурс не найден
- **Business logic errors**: Ошибки бизнес-логики
- **System errors**: Внутренние ошибки системы

## Лимиты и ограничения

- **Максимальная глубина запроса**: 10 уровней
- **Максимальное количество полей**: 100 на запрос
- **Timeout запроса**: 30 секунд
- **Размер payload**: максимум 1MB
- **Rate limiting**: 1000 запросов в минуту на IP
