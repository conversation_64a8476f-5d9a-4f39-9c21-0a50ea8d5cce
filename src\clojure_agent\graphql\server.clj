(ns clojure-agent.graphql.server
  "GraphQL сервер для агентной системы"
  (:require [com.stuartsierra.component :as component]
            [com.walmartlabs.lacinia.pedestal2 :as lp2]
            [io.pedestal.http :as http]
            [io.pedestal.http.cors :as cors]
            [clojure.tools.logging :as log]
            [clojure-agent.graphql.schema :as schema]))

;; ===========================================================================
;; Middleware и перехватчики
;; ===========================================================================

(defn inject-system-interceptor
  "Перехватчик для внедрения системы в контекст GraphQL"
  [system]
  {:name ::inject-system
   :enter (fn [context]
            (assoc-in context [:request :lacinia-app-context :system] system))})

(defn logging-interceptor
  "Перехватчик для логирования GraphQL запросов"
  []
  {:name ::logging
   :enter (fn [context]
            (let [query (get-in context [:request :json-params :query])
                  variables (get-in context [:request :json-params :variables])
                  operation-name (get-in context [:request :json-params :operationName])]
              (log/debug "GraphQL запрос:"
                        {:query query
                         :variables variables
                         :operation-name operation-name}))
            context)
   :leave (fn [context]
            (let [response (get-in context [:response :body])]
              (when (contains? response :errors)
                (log/warn "GraphQL ошибки:" (:errors response))))
            context)})

(defn error-handling-interceptor
  "Перехватчик для обработки ошибок"
  []
  {:name ::error-handling
   :error (fn [context ex]
            (log/error ex "Ошибка при обработке GraphQL запроса")
            (assoc context :response
                   {:status 500
                    :headers {"Content-Type" "application/json"}
                    :body {:errors [{:message "Внутренняя ошибка сервера"
                                     :extensions {:code "INTERNAL_ERROR"}}]}}))})

;; ===========================================================================
;; CORS настройки
;; ===========================================================================

(def cors-policy
  "Политика CORS для GraphQL API"
  {::cors/allowed-origins (constantly true)
   ::cors/creds true
   ::cors/methods #{:get :post :put :delete :options}
   ::cors/allowed-headers #{"Content-Type" "Authorization" "X-Requested-With"}
   ::cors/exposed-headers #{"Content-Type"}})

;; ===========================================================================
;; Конфигурация сервера
;; ===========================================================================

(defn create-service-map
  "Создает карту конфигурации для Pedestal сервера"
  [schema system config]
  (let [port (get config :port 8080)
        host (get config :host "0.0.0.0")
        env (get config :env :dev)]
    (-> (lp2/default-service schema nil)
        (merge {::http/port port
                ::http/host host
                ::http/type :jetty
                ::http/join? false
                ::http/allowed-origins (cors/allow-origin (constantly true))})
        (update ::http/interceptors
                (fn [interceptors]
                  (vec (concat [(inject-system-interceptor system)
                                (logging-interceptor)
                                (error-handling-interceptor)]
                               interceptors))))
        (http/default-interceptors)
        (update ::http/interceptors
                (fn [interceptors]
                  (vec (concat [(cors/cors-interceptor cors-policy)]
                               interceptors)))))))

;; ===========================================================================
;; Компонент GraphQL сервера
;; ===========================================================================

(defrecord GraphQLServer [config system schema server]
  component/Lifecycle

  (start [this]
    (if server
      this
      (try
        (log/info "Запуск GraphQL сервера...")
        (let [compiled-schema (schema/create-schema)
              service-map (create-service-map compiled-schema system config)
              server (-> service-map
                        http/create-server
                        http/start)]
          (log/info (str "GraphQL сервер запущен на "
                        (get config :host "0.0.0.0") ":"
                        (get config :port 8080)))
          (log/info "GraphQL Playground доступен по адресу: /ide")
          (assoc this
                 :schema compiled-schema
                 :server server))
        (catch Exception e
          (log/error e "Ошибка при запуске GraphQL сервера")
          (throw e)))))
  
  (stop [this]
    (when server
      (try
        (log/info "Остановка GraphQL сервера...")
        (http/stop server)
        (log/info "GraphQL сервер остановлен")
        (catch Exception e
          (log/error e "Ошибка при остановке GraphQL сервера"))))
    (assoc this :server nil :schema nil)))

(defn new-graphql-server
  "Создает новый компонент GraphQL сервера"
  [config]
  (map->GraphQLServer {:config config}))

;; ===========================================================================
;; Утилиты для разработки
;; ===========================================================================

(defn print-schema
  "Выводит GraphQL схему в SDL формате"
  []
  (let [schema (schema/create-schema)]
    (println (lp2/schema->sdl schema))))

(defn validate-schema
  "Проверяет валидность GraphQL схемы"
  []
  (try
    (schema/create-schema)
    (log/info "GraphQL схема валидна")
    true
    (catch Exception e
      (log/error e "Ошибка в GraphQL схеме")
      false)))

;; ===========================================================================
;; Примеры запросов для тестирования
;; ===========================================================================

(def example-queries
  "Примеры GraphQL запросов для тестирования"
  {:health-check
   "query HealthCheck {
      health {
        status
        timestamp
        uptime
        version
      }
    }"
   
   :list-agents
   "query ListAgents {
      agents {
        id
        type
        state
        created_at
        updated_at
      }
    }"
   
   :get-agent
   "query GetAgent($id: String!) {
      agent(id: $id) {
        id
        type
        state
        created_at
        updated_at
      }
    }"
   
   :create-agent
   "mutation CreateAgent($input: CreateAgentInput!) {
      createAgent(input: $input) {
        id
        type
        state
        created_at
        updated_at
      }
    }"
   
   :send-message
   "mutation SendMessage($input: SendMessageInput!) {
      sendMessage(input: $input) {
        success
        result
        error
        agent_state
      }
    }"
   
   :metrics
   "query Metrics {
      metrics {
        agents_count
        messages_processed
        memory_usage
        cpu_usage
      }
    }"})

(defn print-example-queries
  "Выводит примеры запросов"
  []
  (doseq [[name query] example-queries]
    (println (str "\n=== " (name name) " ==="))
    (println query)))

;; ===========================================================================
;; Интроспекция схемы
;; ===========================================================================

(def introspection-query
  "Запрос интроспекции для получения полной схемы"
  "query IntrospectionQuery {
    __schema {
      queryType { name }
      mutationType { name }
      subscriptionType { name }
      types {
        ...FullType
      }
      directives {
        name
        description
        locations
        args {
          ...InputValue
        }
      }
    }
  }

  fragment FullType on __Type {
    kind
    name
    description
    fields(includeDeprecated: true) {
      name
      description
      args {
        ...InputValue
      }
      type {
        ...TypeRef
      }
      isDeprecated
      deprecationReason
    }
    inputFields {
      ...InputValue
    }
    interfaces {
      ...TypeRef
    }
    enumValues(includeDeprecated: true) {
      name
      description
      isDeprecated
      deprecationReason
    }
    possibleTypes {
      ...TypeRef
    }
  }

  fragment InputValue on __InputValue {
    name
    description
    type { ...TypeRef }
    defaultValue
  }

  fragment TypeRef on __Type {
    kind
    name
    ofType {
      kind
      name
      ofType {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                }
              }
            }
          }
        }
      }
    }
  }")
