(ns clojure-agent.agent.core-test
  (:require [clojure.test :refer :all]
            [clojure-agent.test-helper :refer :all]
            [clojure-agent.agent.core :as agent]
            [clojure.core.async :as async]
            [taoensso.timbre :as log]))

(use-fixtures :once with-test-system)
(use-fixtures :each with-clean-db with-clean-cache)

(deftest test-agent-creation
  (testing "Создание нового агента"
    (let [system (system/start (system/new-system test-config))
          agent-id "test-agent"
          agent-type :test-type
          agent-params {:param1 "value1"}
          created-agent (agent/create-agent (:agent system) agent-id agent-type agent-params)]
      
      (is (not (nil? created-agent)) "Агент должен быть создан")
      (is (= agent-id (:id created-agent)) "ID агента должен совпадать")
      (is (= agent-type (:type created-agent)) "Тип агента должен совпадать")
      (is (= agent-params (:params created-agent)) "Параметры агента должны совпадать")
      
      (let [found-agent (agent/get-agent (:agent system) agent-id)]
        (is (not (nil? found-agent)) "Агент должен быть найден в системе")
        (is (= agent-id (:id found-agent)) "ID найденного агента должен совпадать"))
      
      (component/stop system))))

(deftest test-message-handling
  (testing "Обработка сообщений агентом"
    (let [system (system/start (system/new-system test-config))
          agent-id "test-message-agent"
          agent-type :test-type
          message {:type :test :data "test-data"}
          response-chan (async/chan)
          handler (fn [agent message reply-fn]
                   (reply-fn {:status :success :data (assoc message :processed true)}))
          
          ;; Создаем агент с обработчиком сообщений
          _ (agent/register-handler (:agent system) agent-type handler)
          created-agent (agent/create-agent (:agent system) agent-id agent-type {})]
      
      (is (not (nil? created-agent)) "Агент должен быть создан")
      
      ;; Отправляем сообщение агенту
      (agent/send-message (:agent system) agent-id message
                         (fn [response]
                           (async/put! response-chan response)))
      
      ;; Ожидаем ответ
      (let [response (async/<!! response-chan)]
        (is (= :success (:status response)) "Статус ответа должен быть :success")
        (is (= :test (get-in response [:data :type])) "Тип сообщения должен совпадать")
        (is (true? (get-in response [:data :processed])) "Сообщение должно быть обработано"))
      
      (component/stop system))))

(deftest test-agent-persistence
  (testing "Сохранение и загрузка состояния агента"
    (let [system (system/start (system/new-system test-config))
          agent-id "persistent-agent"
          agent-type :persistent-type
          initial-state {:counter 0}
          
          ;; Создаем агента с начальным состоянием
          _ (agent/create-agent (:agent system) agent-id agent-type initial-state)
          
          ;; Получаем агента и проверяем начальное состояние
          agent (agent/get-agent (:agent system) agent-id)]
      
      (is (= initial-state (:state @agent)) "Начальное состояние должно совпадать")
      
      ;; Обновляем состояние
      (swap! agent assoc :state {:counter 1})
      
      ;; Перезапускаем систему для проверки сохранения/загрузки
      (component/stop system)
      (let [new-system (system/start (system/new-system test-config))
            reloaded-agent (agent/get-agent (:agent new-system) agent-id)]
        
        (is (not (nil? reloaded-agent)) "Агент должен быть загружен после перезапуска")
        (is (= {:counter 1} (:state @reloaded-agent)) "Состояние должно быть сохранено")
        
        (component/stop new-system)))))
