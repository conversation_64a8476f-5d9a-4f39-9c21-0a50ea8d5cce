# Руководство по развертыванию

## Обзор

Это руководство описывает различные способы развертывания Clojure Agent System в production среде.

## Подготовка к развертыванию

### 1. Сборка приложения

```bash
# Создание production сборки
lein with-profile production uberjar

# Проверка созданного файла
ls -la target/clojure-agent-*-standalone.jar
```

### 2. Конфигурация для production

Создайте файл `resources/config-production.edn`:

```clojure
{:database {:url "jdbc:h2:./data/production/agents"
            :user "sa"
            :password ""
            :driver "org.h2.Driver"
            :pool-size 10}
 
 :graphql {:port 3000
           :host "0.0.0.0"
           :path "/graphql"
           :max-query-depth 10
           :max-query-complexity 1000}
 
 :vectordb {:type :in-memory
            :dimension 100
            :similarity-threshold 0.7
            :max-vectors 100000}
 
 :cache {:default-ttl 600000
         :max-size 10000
         :type :basic}
 
 :metrics {:enabled true
           :collection-interval 30000
           :export-interval 60000}
 
 :logging {:level :info
           :appenders [:file]
           :file-path "./logs/clojure-agent.log"
           :max-file-size "100MB"
           :max-files 10}}
```

### 3. Переменные окружения

```bash
# Создайте файл .env для production
cat > .env << EOF
CLOJURE_AGENT_ENV=production
CLOJURE_AGENT_PORT=3000
CLOJURE_AGENT_HOST=0.0.0.0
CLOJURE_AGENT_DB_PATH=/opt/clojure-agent/data
CLOJURE_AGENT_LOG_LEVEL=info
CLOJURE_AGENT_LOG_PATH=/opt/clojure-agent/logs
CLOJURE_AGENT_METRICS_ENABLED=true
JAVA_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC"
EOF
```

## Развертывание на сервере

### 1. Подготовка сервера

```bash
# Обновление системы (Ubuntu/Debian)
sudo apt update && sudo apt upgrade -y

# Установка Java
sudo apt install openjdk-11-jre-headless -y

# Создание пользователя для приложения
sudo useradd -r -s /bin/false clojure-agent
sudo mkdir -p /opt/clojure-agent/{data,logs,config}
sudo chown -R clojure-agent:clojure-agent /opt/clojure-agent
```

### 2. Установка приложения

```bash
# Копирование файлов
sudo cp target/clojure-agent-*-standalone.jar /opt/clojure-agent/app.jar
sudo cp resources/config-production.edn /opt/clojure-agent/config/config.edn
sudo chown clojure-agent:clojure-agent /opt/clojure-agent/app.jar
```

### 3. Systemd сервис

Создайте файл `/etc/systemd/system/clojure-agent.service`:

```ini
[Unit]
Description=Clojure Agent System
After=network.target
Wants=network.target

[Service]
Type=simple
User=clojure-agent
Group=clojure-agent
WorkingDirectory=/opt/clojure-agent
ExecStart=/usr/bin/java -jar app.jar
ExecReload=/bin/kill -HUP $MAINPID
KillMode=process
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=clojure-agent

# Переменные окружения
Environment=CLOJURE_AGENT_ENV=production
Environment=CLOJURE_AGENT_CONFIG_PATH=/opt/clojure-agent/config/config.edn
Environment=JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC

# Безопасность
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/clojure-agent

[Install]
WantedBy=multi-user.target
```

### 4. Запуск сервиса

```bash
# Перезагрузка systemd
sudo systemctl daemon-reload

# Включение автозапуска
sudo systemctl enable clojure-agent

# Запуск сервиса
sudo systemctl start clojure-agent

# Проверка статуса
sudo systemctl status clojure-agent

# Просмотр логов
sudo journalctl -u clojure-agent -f
```

## Docker развертывание

### 1. Dockerfile

```dockerfile
FROM openjdk:11-jre-slim

# Установка зависимостей
RUN apt-get update && \
    apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

# Создание пользователя
RUN groupadd -r clojure-agent && \
    useradd -r -g clojure-agent clojure-agent

# Создание директорий
RUN mkdir -p /app/data /app/logs /app/config && \
    chown -R clojure-agent:clojure-agent /app

# Копирование приложения
COPY target/clojure-agent-*-standalone.jar /app/app.jar
COPY resources/config-production.edn /app/config/config.edn
RUN chown clojure-agent:clojure-agent /app/app.jar

# Переключение на пользователя
USER clojure-agent
WORKDIR /app

# Переменные окружения
ENV CLOJURE_AGENT_ENV=production
ENV CLOJURE_AGENT_CONFIG_PATH=/app/config/config.edn
ENV JAVA_OPTS="-Xmx1g -Xms512m -XX:+UseG1GC"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Порты
EXPOSE 3000

# Запуск
CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 2. Docker Compose

```yaml
version: '3.8'

services:
  clojure-agent:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - agent_data:/app/data
      - agent_logs:/app/logs
    environment:
      - CLOJURE_AGENT_ENV=production
      - JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - clojure-agent
    restart: unless-stopped

volumes:
  agent_data:
  agent_logs:
```

### 3. Nginx конфигурация

```nginx
events {
    worker_connections 1024;
}

http {
    upstream clojure_agent {
        server clojure-agent:3000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location / {
            proxy_pass http://clojure_agent;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /health {
            proxy_pass http://clojure_agent/health;
            access_log off;
        }
    }
}
```

## Kubernetes развертывание

### 1. Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: clojure-agent
  labels:
    app: clojure-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: clojure-agent
  template:
    metadata:
      labels:
        app: clojure-agent
    spec:
      containers:
      - name: clojure-agent
        image: clojure-agent:latest
        ports:
        - containerPort: 3000
        env:
        - name: CLOJURE_AGENT_ENV
          value: "production"
        - name: JAVA_OPTS
          value: "-Xmx1g -Xms512m -XX:+UseG1GC"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: clojure-agent-data
      - name: logs-volume
        persistentVolumeClaim:
          claimName: clojure-agent-logs
```

### 2. Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: clojure-agent-service
spec:
  selector:
    app: clojure-agent
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: LoadBalancer
```

### 3. Ingress

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: clojure-agent-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: clojure-agent-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: clojure-agent-service
            port:
              number: 80
```

## Мониторинг и логирование

### 1. Prometheus метрики

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'clojure-agent'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

### 2. Grafana дашборд

```json
{
  "dashboard": {
    "title": "Clojure Agent System",
    "panels": [
      {
        "title": "Active Agents",
        "type": "stat",
        "targets": [
          {
            "expr": "clojure_agent_active_count"
          }
        ]
      },
      {
        "title": "Messages Processed",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(clojure_agent_messages_total[5m])"
          }
        ]
      }
    ]
  }
}
```

### 3. ELK Stack для логов

```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /opt/clojure-agent/logs/*.log
  fields:
    service: clojure-agent
  fields_under_root: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
```

## Резервное копирование

### 1. Скрипт резервного копирования

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/clojure-agent"
DATA_DIR="/opt/clojure-agent/data"
DATE=$(date +%Y%m%d_%H%M%S)

# Создание директории для бэкапа
mkdir -p "$BACKUP_DIR"

# Остановка сервиса
sudo systemctl stop clojure-agent

# Создание архива
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" -C "$DATA_DIR" .

# Запуск сервиса
sudo systemctl start clojure-agent

# Удаление старых бэкапов (старше 30 дней)
find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +30 -delete

echo "Backup completed: backup_$DATE.tar.gz"
```

### 2. Cron задача

```bash
# Добавить в crontab
0 2 * * * /opt/clojure-agent/scripts/backup.sh
```

## Безопасность

### 1. Firewall настройки

```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 3000/tcp
sudo ufw enable

# iptables
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
sudo iptables -A INPUT -j DROP
```

### 2. SSL/TLS сертификаты

```bash
# Let's Encrypt с certbot
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### 3. Обновления безопасности

```bash
# Автоматические обновления (Ubuntu)
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

## Масштабирование

### 1. Горизонтальное масштабирование

- Используйте load balancer (nginx, HAProxy)
- Настройте session affinity если необходимо
- Рассмотрите использование внешней базы данных

### 2. Вертикальное масштабирование

```bash
# Увеличение ресурсов JVM
export JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

## Устранение неполадок

### 1. Проверка логов

```bash
# Системные логи
sudo journalctl -u clojure-agent -f

# Логи приложения
tail -f /opt/clojure-agent/logs/clojure-agent.log

# Docker логи
docker logs -f clojure-agent
```

### 2. Проверка здоровья

```bash
# Health check
curl http://localhost:3000/health

# Метрики
curl http://localhost:3000/metrics
```

### 3. Отладка производительности

```bash
# JVM метрики
jstat -gc <pid>
jmap -histo <pid>
```
