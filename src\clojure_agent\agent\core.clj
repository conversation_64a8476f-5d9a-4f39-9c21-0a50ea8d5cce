(ns clojure-agent.agent.core
  "Основной модуль агентной системы"
  (:require [com.stuartsierra.component :as component]
            [clojure.tools.logging :as log]
            [clojure.core.async :as async :refer [<! >! go-loop chan close!]]
            [clojure.spec.alpha :as s]
            [clojure.spec.gen.alpha :as gen]
            [clojure.spec.test.alpha :as stest]))

;; Спецификации
(s/def ::agent-id string?)
(s/def ::agent-type keyword?)
(s/def ::state map?)
(s/def ::message (s/keys :req [::type ::payload]))
(s/def ::handler-fn fn?)

;; Запись агента
(defrecord Agent [id type state handler-fn]
  component/Lifecycle
  (start [this]
    (log/info "Агент запущен:" id "тип:" type)
    this)
  (stop [this]
    (log/info "Агент остановлен:" id)
    this))

;; Менеджер агентов
(defrecord AgentSystem [agents config]
  component/Lifecycle
  (start [this]
    (log/info "Запуск системы агентов...")
    (assoc this :agents (atom {})))
  (stop [this]
    (log/info "Остановка системы агентов...")
    (doseq [[_ agent] @agents]
      (component/stop agent))
    (assoc this :agents nil)))

;; API для работы с агентами
(defn create-agent
  "Создает нового агента"
  [system agent-type id initial-state handler-fn]
  {:pre [(s/valid? ::agent-type agent-type)
         (s/valid? ::agent-id id)
         (s/valid? ::state initial-state)
         (s/valid? ::handler-fn handler-fn)]}
  (let [agent (->Agent id agent-type (atom initial-state) handler-fn)]
    (swap! (:agents system) assoc id agent)
    (component/start agent)
    agent))

(defn send-message
  "Отправляет сообщение агенту"
  [system agent-id message]
  {:pre [(s/valid? ::agent-id agent-id)
         (s/valid? ::message message)]}
  (if-let [agent (get @(:agents system) agent-id)]
    (let [handler (:handler-fn agent)
          state-atom (:state agent)]
      (swap! state-atom #(handler % message))
      @state-atom)
    (throw (ex-info (str "Агент не найден: " agent-id) {:agent-id agent-id}))))

(defn get-agent-state
  "Возвращает текущее состояние агента"
  [system agent-id]
  (some-> system
          :agents
          deref
          (get agent-id)
          :state
          deref))

(defn list-agents
  "Возвращает список всех агентов"
  [system]
  (keys @(:agents system)))

(defn new-agent-system
  "Создает новую систему агентов"
  [config]
  (map->AgentSystem {:config config}))

;; Пример обработчика сообщений
(defn example-handler
  "Пример обработчика сообщений для агента"
  [state message]
  (let [message-type (::type message)
        payload (::payload message)]
    (case message-type
      :update-state (merge state payload)
      :reset {}
      state)))
