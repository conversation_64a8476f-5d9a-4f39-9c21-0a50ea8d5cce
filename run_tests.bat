@echo off
echo Запуск тестов Clojure-агента...

:: Проверяем, установлен ли clj
where clj >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Ошибка: Clojure CLI не найден. Пожалуйста, установите Clojure CLI:
    echo https://clojure.org/guides/install_clojure
    pause
    exit /b 1
)

echo.
echo ================================================
echo Запуск модульных тестов...
echo ================================================
clj -M:test

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ================================================
    echo Обнаружены ошибки в модульных тестах!
    echo ================================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ================================================
echo Запуск интеграционных тестов...
echo ================================================
clj -M:test -m clojure-agent.test-helper/run-integration-tests

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ================================================
    echo Обнаружены ошибки в интеграционных тестах!
    echo ================================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ================================================
echo Все тесты успешно пройдены!
echo ================================================
pause
