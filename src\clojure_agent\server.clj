(ns clojure-agent.server
  (:require [com.stuartsierra.component :as component]
            [ring.adapter.jetty :as jetty]
            [ring.middleware.json :refer [wrap-json-body wrap-json-response]]
            [ring.middleware.params :refer [wrap-params]]
            [ring.middleware.keyword-params :refer [wrap-keyword-params]]
            [ring.middleware.cors :refer [wrap-cors]]
            [ring.middleware.defaults :refer [api-defaults wrap-defaults]]
            [ring.middleware.reload :refer [wrap-reload]]
            [clojure.tools.logging :as log]
            [clojure-agent.api.routes :refer [app-routes]]))

(defn wrap-logging [handler]
  (fn [request]
    (let [start-time (System/currentTimeMillis)
          response (handler request)
          duration (- (System/currentTimeMillis) start-time)]
      (log/info (str (:uri request) " - " (:request-method request) " - " (:status response) " - " duration "ms"))
      response)))

(defn wrap-exception-handling [handler]
  (fn [request]
    (try
      (handler request)
      (catch Exception e
        (log/error e "Ошибка при обработке запроса")
        {:status 500
         :headers {"Content-Type" "application/json"}
         :body {:error "Внутренняя ошибка сервера"
                :details (.getMessage e)}}}))))

(defn create-handler [db agent-system]
  (-> (app-routes db agent-system)
      (wrap-cors :access-control-allow-origin [#".*"]
                 :access-control-allow-methods [:get :post :put :delete :options])
      wrap-keyword-params
      wrap-params
      (wrap-json-body {:keywords? true})
      wrap-json-response
      wrap-exception-handling
      wrap-logging
      (wrap-defaults api-defaults)))

(defrecord HttpServer [config db agent-system server]
  component/Lifecycle
  (start [this]
    (if server
      this
      (let [port (get-in config [:port] 3000)
            host (get-in config [:host] "0.0.0.0")
            handler (create-handler db agent-system)
            server (jetty/run-jetty handler {:port port
                                           :host host
                                           :join? false})]
        (log/info (str "HTTP сервер запущен на " host ":" port))
        (assoc this :server server))))
  (stop [this]
    (when server
      (.stop server)
      (log/info "HTTP сервер остановлен")
      (assoc this :server nil))))

(defn new-server [config]
  (map->HttpServer {:config config}))
