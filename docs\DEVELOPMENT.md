# Руководство по разработке

## Настройка среды разработки

### Требования

- **Java**: OpenJDK 8+ или Oracle JDK 8+
- **Leiningen**: 2.9.0 или новее
- **Git**: для работы с репозиторием
- **IDE**: рекомендуется IntelliJ IDEA с Cursive или Emacs с CIDER

### Установка

```bash
# Клонирование репозитория
git clone <repository-url>
cd clojure-agent

# Проверка Java
java -version

# Установка Leiningen (если не установлен)
# macOS
brew install leiningen

# Ubuntu/Debian
sudo apt-get install leiningen

# Windows - скачать с https://leiningen.org/

# Установка зависимостей
lein deps
```

### Настройка IDE

#### IntelliJ IDEA + Cursive

1. Установите плагин Cursive
2. Откройте проект как Leiningen project
3. Настройте REPL: Run → Edit Configurations → Add Clojure REPL → Local

#### Emacs + CIDER

```elisp
;; Добавьте в .emacs или init.el
(require 'cider)
(setq cider-repl-display-help-banner nil)
(setq cider-repl-pop-to-buffer-on-connect 'display-only)
```

#### VS Code + Calva

1. Установите расширение Calva
2. Откройте проект
3. Ctrl+Shift+P → "Calva: Start a Project REPL"

## Структура проекта

```
clojure-agent/
├── project.clj              # Конфигурация проекта
├── README.md                # Основная документация
├── resources/
│   ├── config.edn          # Конфигурация
│   └── migrations/         # Миграции БД
├── src/clojure_agent/
│   ├── core.clj            # Точка входа
│   ├── system.clj          # Главный компонент системы
│   ├── config.clj          # Управление конфигурацией
│   ├── agent/              # Модуль агентов
│   │   ├── core.clj        # Основная логика агентов
│   │   ├── state.clj       # Управление состоянием
│   │   └── messages.clj    # Обработка сообщений
│   ├── db/                 # Модуль базы данных
│   │   ├── core.clj        # Основная работа с БД
│   │   ├── migrations.clj  # Миграции
│   │   └── vectordb.clj    # Векторная БД
│   ├── graphql/            # GraphQL модуль
│   │   ├── schema.clj      # Схема GraphQL
│   │   ├── resolvers.clj   # Резолверы
│   │   └── server.clj      # HTTP сервер
│   ├── metrics.clj         # Система метрик
│   └── cache.clj           # Кэширование
├── test/clojure_agent/     # Тесты
└── docs/                   # Документация
```

## Рабочий процесс разработки

### 1. REPL-driven development

```clojure
;; Запуск REPL
lein repl

;; Загрузка системы
(require '[clojure-agent.system :as system])
(require '[com.stuartsierra.component :as component])

;; Создание и запуск системы
(def sys (component/start (system/create-system)))

;; Остановка системы
(def sys (component/stop sys))

;; Перезагрузка кода
(require '[clojure-agent.core :reload])
```

### 2. Интерактивная разработка

```clojure
;; Тестирование функций в REPL
(require '[clojure-agent.agent.core :as agent])

;; Создание тестового агента
(def test-agent (agent/create-agent "test-001" "worker" {}))

;; Отправка сообщения
(agent/send-message test-agent {:type "test" :data "hello"})

;; Проверка состояния
(agent/get-state test-agent)
```

### 3. Горячая перезагрузка

```clojure
;; Использование tools.namespace для перезагрузки
(require '[clojure.tools.namespace.repl :refer [refresh]])

;; Перезагрузка измененного кода
(refresh)
```

## Стандарты кода

### Именование

```clojure
;; Функции и переменные - kebab-case
(defn create-agent [id type state])
(def default-config {...})

;; Константы - SCREAMING_SNAKE_CASE
(def MAX_AGENTS 1000)
(def DEFAULT_TIMEOUT 30000)

;; Предикаты заканчиваются на ?
(defn agent-active? [agent])
(defn valid-config? [config])

;; Преобразующие функции заканчиваются на !
(defn update-agent-state! [agent new-state])
```

### Документация

```clojure
(defn create-agent
  "Создает новый агент с указанными параметрами.
  
  Параметры:
  - id: уникальный идентификатор агента
  - type: тип агента (keyword)
  - initial-state: начальное состояние (map)
  
  Возвращает:
  Новый экземпляр агента или nil в случае ошибки."
  [id type initial-state]
  ;; implementation
  )
```

### Обработка ошибок

```clojure
;; Используйте ex-info для структурированных ошибок
(defn validate-agent-id [id]
  (when-not (string? id)
    (throw (ex-info "Agent ID must be a string"
                    {:type :validation-error
                     :field :id
                     :value id}))))

;; Используйте try-catch для обработки ошибок
(defn safe-operation [agent]
  (try
    (risky-operation agent)
    (catch Exception e
      (log/error e "Failed to perform operation on agent" {:agent-id (:id agent)})
      nil)))
```

## Тестирование

### Структура тестов

```clojure
(ns clojure-agent.agent.core-test
  (:require [clojure.test :refer :all]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.test-utils :as utils]))

(deftest test-create-agent
  (testing "Creating agent with valid parameters"
    (let [agent (agent/create-agent "test-001" :worker {})]
      (is (= "test-001" (:id agent)))
      (is (= :worker (:type agent)))))
  
  (testing "Creating agent with invalid parameters"
    (is (thrown? Exception (agent/create-agent nil :worker {})))))
```

### Запуск тестов

```bash
# Все тесты
lein test

# Конкретный namespace
lein test clojure-agent.agent.core-test

# Тесты с покрытием
lein cloverage

# Автоматический запуск при изменениях
lein test-refresh
```

### Тестирование компонентов

```clojure
(deftest test-system-lifecycle
  (testing "System starts and stops correctly"
    (let [system (component/start (create-test-system))]
      (is (component/started? system))
      (let [stopped-system (component/stop system)]
        (is (not (component/started? stopped-system)))))))
```

## Отладка

### Логирование

```clojure
(require '[clojure.tools.logging :as log])

;; Различные уровни логирования
(log/trace "Detailed trace information")
(log/debug "Debug information" {:agent-id "test-001"})
(log/info "General information")
(log/warn "Warning message")
(log/error "Error occurred" exception-object)
```

### Профилирование

```clojure
;; Использование criterium для бенчмарков
(require '[criterium.core :as criterium])

(criterium/bench (expensive-operation))

;; Простое измерение времени
(time (some-operation))
```

### Отладка в REPL

```clojure
;; Использование pprint для красивого вывода
(require '[clojure.pprint :refer [pprint]])
(pprint complex-data-structure)

;; Трассировка выполнения функций
(require '[clojure.tools.trace :refer [trace-forms]])
(trace-forms (complex-function args))
```

## Работа с базой данных

### Миграции

```clojure
;; Создание новой миграции
(require '[clojure-agent.db.migrations :as migrations])

(migrations/create-migration "add-agent-metadata-table")
```

### Тестирование с БД

```clojure
(deftest test-with-database
  (utils/with-test-db
    (testing "Database operations"
      ;; тесты с реальной БД
      )))
```

## GraphQL разработка

### Тестирование схемы

```clojure
(require '[com.walmartlabs.lacinia :as lacinia])

(deftest test-graphql-schema
  (let [result (lacinia/execute schema query variables nil)]
    (is (nil? (:errors result)))
    (is (= expected-data (:data result)))))
```

### Отладка резолверов

```clojure
(defn debug-resolver [resolver-fn]
  (fn [context args value]
    (log/debug "Resolver called" {:args args :value value})
    (let [result (resolver-fn context args value)]
      (log/debug "Resolver result" {:result result})
      result)))
```

## Производительность

### Профилирование памяти

```bash
# Запуск с профилированием
lein with-profile +profiling run

# Анализ heap dump
jmap -dump:format=b,file=heap.hprof <pid>
```

### Оптимизация

```clojure
;; Используйте transients для множественных изменений
(defn build-large-map [items]
  (persistent!
    (reduce (fn [acc item]
              (assoc! acc (:id item) item))
            (transient {})
            items)))

;; Ленивые последовательности для больших данных
(defn process-large-dataset [dataset]
  (->> dataset
       (map transform-item)
       (filter valid-item?)
       (take 1000)))
```

## Развертывание

### Создание uberjar

```bash
lein uberjar
```

### Профили сборки

```clojure
;; В project.clj
:profiles {:dev {:dependencies [[criterium "0.4.6"]]}
           :production {:aot :all
                       :omit-source true}}
```

### Переменные окружения

```bash
# Для разработки
export CLOJURE_AGENT_ENV=development
export CLOJURE_AGENT_LOG_LEVEL=debug

# Для production
export CLOJURE_AGENT_ENV=production
export CLOJURE_AGENT_LOG_LEVEL=info
```

## Лучшие практики

### 1. Функциональный стиль
- Предпочитайте неизменяемые структуры данных
- Избегайте побочных эффектов в чистых функциях
- Используйте композицию функций

### 2. Управление состоянием
- Минимизируйте изменяемое состояние
- Используйте atoms для простого состояния
- Refs для координированных изменений

### 3. Обработка ошибок
- Используйте ex-info для структурированных ошибок
- Логируйте все ошибки с контекстом
- Предоставляйте осмысленные сообщения об ошибках

### 4. Тестирование
- Пишите тесты для всех публичных функций
- Используйте property-based тестирование для сложной логики
- Тестируйте граничные случаи

### 5. Документация
- Документируйте все публичные функции
- Используйте примеры в docstrings
- Поддерживайте документацию в актуальном состоянии
