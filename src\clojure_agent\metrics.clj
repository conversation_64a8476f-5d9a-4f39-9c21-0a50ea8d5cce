(ns clojure-agent.metrics
  "Модуль для сбора и экспорта метрик приложения."
  (:require [clojure.tools.logging :as log]
            [com.stuartsierra.component :as component]
            [metrics.core :as metrics]
            [metrics.reporters.jmx :as jmx]
            [metrics.reporters.console :as console]
            [metrics.gauges :as gauges]
            [metrics.meters :as meters]
            [metrics.timers :as timers]
            [metrics.counters :as counters]
            [clojure.string :as str]))

;; ===========================================================================
;; Внутренние утилиты
;; ===========================================================================

(defn- normalize-name
  "Нормализует имя метрики для хранения в реестре."
  [& parts]
  (->> parts
       (map (fn [part]
              (cond
                (keyword? part) (name part)
                (string? part) part
                :else (str part))))
       (remove empty?)
       (map (fn [s] (str/replace s #"[^a-zA-Z0-9_\-\.]" "_")))
       (into-array String)))

(defn- safe-register
  "Безопасно регистрирует метрику, перехватывая исключения."
  [registry metric-fn metric-name]
  (try
    (metric-fn registry metric-name)
    (catch Exception e
      (log/error e "Ошибка при регистрации метрики:" (str/join "." metric-name))
      nil)))

;; ===========================================================================
;; Компонент системы метрик
;; ===========================================================================

(defrecord MetricsSystem [config registry jmx-reporter console-reporter]
  component/Lifecycle
  (start [this]
    (log/info "Запуск системы метрик...")
    (let [registry (or registry (metrics/new-registry))
          jmx-enabled (get-in config [:jmx :enabled] true)
          jmx-reporter (when jmx-enabled
                         (log/info "Включен JMX репортер метрик")
                         (jmx/reporter registry (get-in config [:jmx :domain] "clojure.agent")))
          
          console-enabled (get-in config [:console :enabled] true)
          console-reporter (when console-enabled
                             (log/info "Включен консольный репортер метрик")
                             (let [reporter (console/reporter registry {})]
                               (console/start reporter (get-in config [:console :report-interval] 60)))
                             reporter)]
      
      (log/info "Система метрик запущена")
      (assoc this
             :registry registry
             :jmx-reporter jmx-reporter
             :console-reporter console-reporter)))
  
  (stop [this]
    (log/info "Остановка системы метрик...")
    (when jmx-reporter
      (log/debug "Остановка JMX репортера метрик")
      (jmx/stop jmx-reporter))
    
    (when console-reporter
      (log/debug "Остановка консольного репортера метрик")
      (console/stop console-reporter))
    
    (log/info "Система метрик остановлена")
    (assoc this
           :registry nil
           :jmx-reporter nil
           :console-reporter nil))
  
  ;; Метрики
  IMetrics
  (counter [this name] (counters/counter registry (normalize-name name)))
  (meter [this name] (meters/meter registry (normalize-name name)))
  (timer [this name] (timers/timer registry (normalize-name name)))
  (gauge [this name gauge-fn] (gauges/gauge-fn registry (normalize-name name) gauge-fn)))

;; ===========================================================================
;; Публичный API
;; ===========================================================================

(defprotocol IMetrics
  "Протокол для работы с метриками."
  (counter [this name] "Создает или получает счетчик с указанным именем.")
  (meter [this name] "Создает или получает измеритель с указанным именем.")
  (timer [this name] "Создает или получает таймер с указанным именем.")
  (gauge [this name gauge-fn] "Создает или получает датчик с указанным именем и функцией."))

(defn new-metrics-system
  "Создает новую систему метрик с указанной конфигурацией."
  [config]
  (map->MetricsSystem {:config config}))

;; ===========================================================================
;; Удобные функции для работы с метриками
;; ===========================================================================

(defn inc-counter!
  "Увеличивает значение счетчика на 1."
  ([metrics name] (inc-counter! metrics name 1))
  ([metrics name n]
   (when-let [c (counter metrics name)]
     (counters/inc! c n))))

(defn mark-meter!
  "Отмечает событие в измерителе."
  [metrics name]
  (when-let [m (meter metrics name)]
    (meters/mark! m)))

(defn time-call
  "Измеряет время выполнения функции с помощью таймера."
  [metrics name f]
  (if-let [t (timer metrics name)]
    (timers/time! t f)
    (f)))

(defmacro with-timer
  "Измеряет время выполнения блока кода с помощью таймера."
  [metrics name & body]
  `(time-call ~metrics ~name (fn [] ~@body)))

(defn register-gauge
  "Регистрирует функцию для периодического сбора метрик."
  [metrics name gauge-fn]
  (gauge metrics name gauge-fn))

;; ===========================================================================
;; Предопределенные метрики
;; ===========================================================================

(defn register-jvm-metrics
  "Регистрирует стандартные метрики JVM."
  [metrics]
  (let [runtime (Runtime/getRuntime)
        mb 1024.0 1024.0]
    ;; Использование памяти
    (register-gauge metrics ["jvm" "memory" "heap" "used"]
                   #(float (/ (- (.totalMemory runtime) (.freeMemory runtime)) mb)))
    
    (register-gauge metrics ["jvm" "memory" "heap" "max"]
                   #(float (/ (.maxMemory runtime) mb)))
    
    (register-gauge metrics ["jvm" "memory" "non-heap" "used"]
                   #(float (/ (- (.totalMemory runtime) (.freeMemory runtime)) mb)))
    
    ;; Количество потоков
    (register-gauge metrics ["jvm" "threads" "count"]
                   #(count (.getThreadIds (java.lang.management.ManagementFactory/getThreadMXBean))))
    
    ;; Время работы
    (let [uptime (java.lang.management.ManagementFactory/getRuntimeMXBean)]
      (register-gauge metrics ["jvm" "uptime"]
                     #(.getUptime uptime)))
    
    metrics))
