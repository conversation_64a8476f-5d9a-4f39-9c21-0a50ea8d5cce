(ns clojure-agent.test-helper
  "Вспомогательные функции для тестирования."
  (:require [clojure.test :refer :all]
            [com.stuartsierra.component :as component]
            [next.jdbc :as jdbc]
            [clojure-agent.system :as system]
            [clojure-agent.config :as config]
            [clojure-agent.db.core :as db]
            [taoensso.timbre :as log]
            [clojure.string :as str]))

;; ===========================================================================
;; Настройка тестового окружения
;; ===========================================================================

(def test-config
  "Тестовая конфигурация."
  {:http {:port 0
           :host "localhost"
           :join? false
           :nrepl-port 0}
   :database {:dbtype "h2"
              :dbname "./target/test-db"
              :user "sa"
              :password ""
              :naming {:keys str/lower-case
                       :fields str/upper-case}}
   :logging {:level :info
             :console? true
             :min-level :info}
   :metrics {:enabled true
             :jmx {:enabled true}
             :console {:enabled false}}
   :cache {:default {:type :ttl
                     :ttl 300
                     :max-size 1000}
           :caches {:sessions {:type :lru
                               :max-size 100}
                    :tokens {:type :ttl
                             :ttl 3600
                             :max-size 10000}}}}
   :agent {:default-timeout 5000
           :max-queue-size 10000
           :threads 4
           :metrics {:enabled true}
           :persistence {:enabled true
                         :interval 30000}}})

(defn with-test-system
  "Фикстура для запуска системы в тестах."
  [f]
  (log/set-level! :info) ; Уменьшаем уровень логирования в тестах
  (let [system (-> (system/new-system test-config)
                   (component/start))]
    (try
      (f system)
      (finally
        (component/stop system)))))

(defn with-db-cleanup
  "Фикстура для очистки базы данных перед каждым тестом."
  [f]
  (let [db-spec (get-in test-config [:database])]
    (jdbc/execute! db-spec ["DROP ALL OBJECTS"])
    (db/init-database! {:db-spec db-spec})
    (f)))

(defn with-clean-cache
  "Фикстура для очистки кэша перед каждым тестом."
  [f]
  (f))

;; ===========================================================================
;; Вспомогательные функции для тестирования
;; ===========================================================================

(defn is-valid-uuid?
  "Проверяет, является ли строка валидным UUID."
  [s]
  (try
    (java.util.UUID/fromString s)
    true
    (catch Exception _ false)))

(defmacro is-valid-uuid
  "Проверяет, что значение является валидным UUID."
  [x]
  `(is (is-valid-uuid? ~x) (str "Expected a valid UUID, got: " (pr-str ~x))))

(defmacro throws-with-msg?
  "Проверяет, что выражение выбрасывает исключение с указанным сообщением."
  [ex-class re msg]
  `(try
     ~msg
     (is false (str "Expected exception " ~(str ex-class) " with message matching " ~(str re)))
     (catch ~ex-class e#
       (is (re-find ~re (.getMessage e#)) ~(str "Exception message does not match: " (str re))))))

(defn http-get
  "Выполняет HTTP GET запрос к тестовой системе."
  ([system path] (http-get system path nil))
  ([system path headers]
   (let [port (get-in system [:http-server :server-port])]
     (http/request
       {:method :get
        :url (str "http://localhost:" port path)
        :headers (merge {"Accept" "application/json"} headers)}))))

(defn http-post
  "Выполняет HTTP POST запрос к тестовой системе."
  ([system path body] (http-post system path body nil))
  ([system path body headers]
   (let [port (get-in system [:http-server :server-port])]
     (http/request
       {:method :post
        :url (str "http://localhost:" port path)
        :headers (merge {"Content-Type" "application/json"
                        "Accept" "application/json"} headers)
        :body (json/write-str body)}))))

(defn http-put
  "Выполняет HTTP PUT запрос к тестовой системе."
  ([system path body] (http-put system path body nil))
  ([system path body headers]
   (let [port (get-in system [:http-server :server-port])]
     (http/request
       {:method :put
        :url (str "http://localhost:" port path)
        :headers (merge {"Content-Type" "application/json"
                        "Accept" "application/json"} headers)
        :body (json/write-str body)}))))

(defn http-delete
  "Выполняет HTTP DELETE запрос к тестовой системе."
  ([system path] (http-delete system path nil))
  ([system path headers]
   (let [port (get-in system [:http-server :server-port])]
     (http/request
       {:method :delete
        :url (str "http://localhost:" port path)
        :headers (merge {"Accept" "application/json"} headers)}))))

(defn parse-json-response
  "Парсит JSON-ответ в хеш-таблицу."
  [response]
  (update response :body #(when % (json/read-str % :key-fn keyword))))

;; ===========================================================================
;; Макросы для тестирования
;; ===========================================================================

(defmacro with-system
  "Выполняет тесты с запущенной системой."
  [system-sym & body]
  `(with-test-system
     (fn [~system-sym] ~@body)))

(defmacro with-clean-db
  "Выполняет тесты с очищенной базой данных."
  [& body]
  `(with-db-cleanup (fn [] ~@body)))

(defmacro with-clean-cache
  "Выполняет тесты с очищенным кэшем."
  [& body]
  `(with-clean-cache (fn [] ~@body)))

;; ===========================================================================
;; Утилиты для работы с агентами в тестах
;; ===========================================================================

(defn await-agent
  "Ожидает завершения обработки сообщений агентом."
  [agent-id timeout-ms]
  (let [agent (get-agent agent-id)]
    (when agent
      (await-for timeout-ms agent))))

(defn get-agent-state
  "Возвращает состояние агента."
  [system agent-id]
  (when-let [agent-system (:agent system)]
    (agent/get-state agent-system agent-id)))

(defn send-message
  "Отправляет сообщение агенту."
  [system agent-id message]
  (when-let [agent-system (:agent system)]
    (agent/send-message agent-system agent-id message)))

;; ===========================================================================
;; Утилиты для проверки ответов API
;; ===========================================================================

(defn assert-success-response
  "Проверяет, что ответ успешный."
  [response]
  (is (= 200 (:status response)))
  (is (contains? (:body response) :success))
  (is (true? (:success (:body response)))))

(defn assert-error-response
  "Проверяет, что ответ содержит ошибку."
  [response expected-status expected-error]
  (is (= expected-status (:status response)))
  (is (contains? (:body response) :success))
  (is (false? (:success (:body response))))
  (when expected-error
    (is (= expected-error (get-in response [:body :error])))))

(defn assert-validation-error
  "Проверяет, что ответ содержит ошибку валидации."
  [response field]
  (assert-error-response response 400 "Ошибка валидации")
  (is (contains? (get-in response [:body :details]) field)))

;; ===========================================================================
;; Утилиты для работы с базой данных в тестах
;; ===========================================================================

(defn count-rows
  "Возвращает количество строк в таблице."
  [system table]
  (let [db (get-in system [:db :datasource])]
    (:count (first (jdbc/query db [(str "SELECT COUNT(*) as count FROM " (name table))])))))

(defn table-exists?
  "Проверяет существование таблицы в базе данных."
  [system table-name]
  (let [db (get-in system [:db :datasource])
        sql (str "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES "
                 "WHERE TABLE_NAME = ?")]
    (pos? (:count (first (jdbc/query db [sql (name table-name)]))))))

(defn column-exists?
  "Проверяет существование колонки в таблице."
  [system table-name column-name]
  (let [db (get-in system [:db :datasource])
        sql (str "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS "
                 "WHERE TABLE_NAME = ? AND COLUMN_NAME = ?")]
    (pos? (:count (first (jdbc/query db [sql (name table-name) (name column-name)]))))))
