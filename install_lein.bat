@echo off
echo === Installing Leiningen ===

echo.
echo Checking Java...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Java not found. Please install Java 8 or later first.
    echo Download from: https://adoptium.net/
    pause
    exit /b 1
)
echo [OK] Java found

echo.
echo Creating Leiningen directory...
if not exist "%USERPROFILE%\.lein" (
    mkdir "%USERPROFILE%\.lein"
    echo [OK] Created .lein directory
) else (
    echo [OK] .lein directory exists
)

echo.
echo Downloading lein.bat...
powershell -Command "Invoke-WebRequest -Uri 'https://raw.githubusercontent.com/technomancy/leiningen/stable/bin/lein.bat' -OutFile '%USERPROFILE%\.lein\lein.bat'"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to download lein.bat
    pause
    exit /b 1
)
echo [OK] Downloaded lein.bat

echo.
echo Adding to PATH...
setx PATH "%PATH%;%USERPROFILE%\.lein" >nul 2>&1
echo [OK] Added to PATH (restart terminal to take effect)

echo.
echo Testing installation...
"%USERPROFILE%\.lein\lein.bat" version
if %errorlevel% neq 0 (
    echo [WARNING] Test failed, but lein.bat is installed
    echo You may need to restart your terminal
)

echo.
echo === Installation Complete ===
echo.
echo Next steps:
echo 1. Restart your terminal (or open new one)
echo 2. Run: lein version
echo 3. Run: lein deps (to download dependencies)
echo 4. Run: lein run (to start the GraphQL server)
echo.

pause
