(ns clojure-agent.core
  (:require [com.stuartsierra.component :as component]
            [clojure.tools.logging :as log]
            [clojure-agent.system :as system]
            [clojure-agent.config :as config])
  (:gen-class))

(defn -main
  "Точка входа в приложение"
  [& args]
  (let [system (system/new-system (config/load-config))]
    (log/info "Запуск системы...")
    (alter-var-root #'system/current-system (constantly (component/start system)))
    (.addShutdownHook (Runtime/getRuntime)
                      (Thread. ^Runnable (fn []
                                         (log/info "Остановка системы...")
                                         (component/stop system))))))
