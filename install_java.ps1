# Скрипт для установки Java (Temurin JDK 21)

# Параметры установки
$javaVersion = "21"
$installDir = "$env:ProgramFiles\Eclipse Adoptium"
$downloadUrl = "https://github.com/adoptium/temurin21-binaries/releases/download/jdk-21.0.3+9/OpenJDK21U-jdk_x64_windows_hotspot_21.0.3_9.msi"
$msiPath = "$env:TEMP\temurin-jdk-$javaVersion.msi"

# Проверяем права администратора
$isAdmin = ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "Для установки Java требуются права администратора." -ForegroundColor Yellow
    Write-Host "Пожалуйста, запустите этот скрипт от имени администратора." -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "=== Установка Eclipse Temurin JDK $javaVersion ===" -ForegroundColor Cyan

# Создаем временную директорию, если её нет
if (-not (Test-Path -Path $env:TEMP)) {
    New-Item -ItemType Directory -Path $env:TEMP | Out-Null
}

# Скачиваем установщик
Write-Host "Скачивание установщика JDK..."
try {
    Invoke-WebRequest -Uri $downloadUrl -OutFile $msiPath -UseBasicParsing
    Write-Host "Установщик успешно загружен." -ForegroundColor Green
} catch {
    Write-Host "Ошибка при загрузке установщика: $_" -ForegroundColor Red
    pause
    exit 1
}

# Устанавливаем JDK
Write-Host "Установка JDK..."
try {
    $arguments = "/i `"$msiPath`" /qn INSTALLDIR=`"$installDir`" ADDLOCAL=FeatureMain,FeatureEnvironment,FeatureJarFileRunWith,FeatureJavaHome,FeatureOracleJavaSoft"
    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $arguments -Wait -NoNewWindow -PassThru
    
    if ($process.ExitCode -ne 0) {
        throw "Ошибка установки. Код выхода: $($process.ExitCode)"
    }
    
    Write-Host "JDK успешно установлен в $installDir" -ForegroundColor Green
} catch {
    Write-Host "Ошибка при установке JDK: $_" -ForegroundColor Red
    if (Test-Path $msiPath) { Remove-Item $msiPath }
    pause
    exit 1
}

# Добавляем Java в PATH
$javaBinPath = "$installDir\jdk-$javaVersion.0.3+9\bin"
$currentPath = [Environment]::GetEnvironmentVariable('Path', 'Machine')

if ($currentPath -notlike "*$javaBinPath*") {
    Write-Host "Добавление Java в PATH..."
    [Environment]::SetEnvironmentVariable('Path', "$currentPath;$javaBinPath", 'Machine')
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    Write-Host "Java добавлена в PATH" -ForegroundColor Green
}

# Проверяем установку
Write-Host "`nПроверка установки..."
try {
    $javaVersionOutput = & java -version 2>&1
    Write-Host "Java успешно установлена:" -ForegroundColor Green
    $javaVersionOutput | ForEach-Object { Write-Host $_ }
} catch {
    Write-Host "Предупреждение: не удалось проверить версию Java. Возможно, требуется перезапуск терминала." -ForegroundColor Yellow
}

# Удаляем установщик
if (Test-Path $msiPath) { Remove-Item $msiPath }

Write-Host "`nУстановка завершена!" -ForegroundColor Green
Write-Host "Возможно, потребуется перезапустить терминал для применения изменений PATH." -ForegroundColor Yellow

# Устанавливаем JAVA_HOME, если ещё не установлен
if (-not [Environment]::GetEnvironmentVariable('JAVA_HOME', 'Machine')) {
    $javaHome = "$installDir\jdk-$javaVersion.0.3+9"
    [Environment]::SetEnvironmentVariable('JAVA_HOME', $javaHome, 'Machine')
    Write-Host "Установлена переменная окружения JAVA_HOME: $javaHome" -ForegroundColor Green
}

pause
