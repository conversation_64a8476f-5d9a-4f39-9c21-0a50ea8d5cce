(ns clojure-agent.api.routes-test
  (:require [clojure.test :refer :all]
            [clojure.string :as str]
            [clojure.data.json :as json]
            [ring.mock.request :as mock]
            [ring.middleware.params :refer [wrap-params]]
            [ring.middleware.keyword-params :refer [wrap-keyword-params]]
            [ring.middleware.json :refer [wrap-json-body wrap-json-response]]
            [clojure-agent.test-helper :refer :all]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.api.routes :as routes]
            [clojure-agent.db.core :as db]
            [clojure-agent.metrics :as metrics]
            [clojure.spec.test.alpha :as stest]))

(use-fixtures :once with-test-system)
(use-fixtures :each with-clean-db with-clean-cache)

(defn parse-json
  "Парсит JSON-строку в структуру данных Clojure."
  [s]
  (json/read-str s :key-fn keyword))

(defn request
  "Создает тестовый HTTP-запрос."
  ([method uri] (request method uri nil nil))
  ([method uri body] (request method uri body nil))
  ([method uri body headers]
   (let [req (-> (mock/request method uri)
                 (mock/content-type "application/json")
                 (mock/header "Accept" "application/json")
                 (mock/header "X-Request-ID" (str (java.util.UUID/randomUUID))))
         req (if body
               (mock/body req (json/write-str body))
               req)
         req (if headers
               (reduce-kv (fn [r k v] (mock/header r (name k) v)) req headers)
               req)]
     req)))

(defn response->data
  "Извлекает и парсит тело JSON-ответа."
  [response]
  (when-let [body (:body response)]
    (if (string? body)
      (parse-json body)
      body)))

(deftest test-api-version-header
  (testing "Проверка заголовка версии API"
    (with-system [system (system/start (system/new-system test-config))]
      (let [app (routes/app-routes (:db system) (:agent system))
            response (app (request :get "/api/v1/health"))
            api-version (get-in response [:headers "X-API-Version"])]
        (is (= "1.0.0" api-version) "API версия должна быть указана в заголовке")))))

(deftest test-health-check
  (testing "Проверка эндпоинта health"
    (with-system [system (system/start (system/new-system test-config))]
      (let [app (routes/app-routes (:db system) (:agent system))
            response (app (request :get "/api/v1/health"))
            body (response->data response)]
        (is (= 200 (:status response)) "Статус ответа должен быть 200")
        (is (= true (:success body)) "Ответ должен быть успешным")
        (is (= "ok" (get-in body [:data :status])) "Статус должен быть 'ok'")
        (is (number? (get-in body [:data :timestamp])) "Должно быть указано время ответа")))))

(deftest test-create-agent
  (testing "Создание нового агента"
    (with-system [system (system/start (system/new-system test-config))]
      (let [app (routes/app-routes (:db system) (:agent system))
            agent-data {:id "test-agent" :type :test-type :params {:key "value"}}
            response (app (request :post "/api/v1/agents" agent-data))
            body (response->data response)]
        
        (is (= 201 (:status response)) "Статус ответа должен быть 201")
        (is (:success body) "Ответ должен быть успешным")
        (is (= "test-agent" (get-in body [:data :id])) "ID агента должен совпадать")
        (is (= :test-type (get-in body [:data :type])) "Тип агента должен совпадать")
        (is (= {:key "value"} (get-in body [:data :params])) "Параметры агента должны совпадать")
        
        (testing "Попытка создать агента с существующим ID"
          (let [response (app (request :post "/api/v1/agents" agent-data))
                body (response->data response)]
            (is (= 409 (:status response)) "При дублировании ID должен быть статус 409")
            (is (false? (:success body)) "Ответ должен быть неудачным")
            (is (string? (:error body)) "Должно быть сообщение об ошибке")))))))

(deftest test-get-agent
  (testing "Получение информации об агенте"
    (with-system [system (system/start (system/new-system test-config))]
      (let [agent-system (:agent system)
            _ (agent/create-agent agent-system "test-get" :test-type {:test "data"})
            app (routes/app-routes (:db system) agent-system)
            
            ;; Успешное получение агента
            response1 (app (request :get "/api/v1/agents/test-get"))
            body1 (response->data response1)
            
            ;; Получение несуществующего агента
            response2 (app (request :get "/api/v1/agents/non-existent"))
            body2 (response->data response2)]
        
        (is (= 200 (:status response1)) "Статус ответа должен быть 200")
        (is (:success body1) "Ответ должен быть успешным")
        (is (= "test-get" (get-in body1 [:data :id])) "ID агента должен совпадать")
        
        (is (= 404 (:status response2)) "Для несуществующего агента должен быть статус 404")
        (is (false? (:success body2)) "Ответ должен быть неудачным")
        (is (string? (:error body2)) "Должно быть сообщение об ошибке")))))

(deftest test-send-message
  (testing "Отправка сообщения агенту"
    (with-system [system (system/start (system/new-system test-config))]
      (let [agent-system (:agent system)
            agent-id "test-message"
            _ (agent/register-handler agent-system :test-type
                (fn [agent message reply-fn]
                  (reply-fn {:status :received :message message})))
            _ (agent/create-agent agent-system agent-id :test-type {})
            app (routes/app-routes (:db system) agent-system)
            
            ;; Успешная отправка сообщения
            message {:type :test :data "test-data"}
            response1 (app (request :post (str "/api/v1/agents/" agent-id "/messages") message))
            body1 (response->data response1)
            
            ;; Отправка сообщения несуществующему агенту
            response2 (app (request :post "/api/v1/agents/non-existent/messages" message))
            body2 (response->data response2)
            
            ;; Некорректный формат сообщения
            invalid-message {:invalid "format"}
            response3 (app (request :post (str "/api/v1/agents/" agent-id "/messages") invalid-message))
            body3 (response->data response3)]
        
        (is (= 200 (:status response1)) "Статус ответа должен быть 200")
        (is (:success body1) "Ответ должен быть успешным")
        (is (= :received (get-in body1 [:data :status])) "Статус обработки должен быть :received")
        (is (= "test-data" (get-in body1 [:data :message :data])) "Данные сообщения должны совпадать")
        
        (is (= 404 (:status response2)) "Для несуществующего агента должен быть статус 404")
        (is (false? (:success body2)) "Ответ должен быть неудачным")
        
        (is (= 400 (:status response3)) "При невалидном сообщении должен быть статус 400")
        (is (false? (:success body3)) "Ответ должен быть неудачным")
        (is (string? (:error body3)) "Должно быть сообщение об ошибке")
        (is (map? (get-in body3 [:details :clojure.spec.alpha/problems])) "Должны быть детали ошибки валидации")))))

(deftest test-validation
  (testing "Валидация входных данных"
    (with-system [system (system/start (system/new-system test-config))]
      (let [app (routes/app-routes (:db system) (:agent system))
            
            ;; Создание агента без обязательных полей
            response1 (app (request :post "/api/v1/agents" {}))
            body1 (response->data response1)
            
            ;; Создание агента с неверными типами
            response2 (app (request :post "/api/v1/agents" {:id 123 :type "not-a-keyword" :params "not-a-map"}))
            body2 (response->data response2)]
        
        (is (= 400 (:status response1)) "Статус ответа должен быть 400")
        (is (false? (:success body1)) "Ответ должен быть неудачным")
        (is (= "Ошибка валидации" (:error body1)) "Должно быть сообщение об ошибке валидации")
        (is (map? (:details body1)) "Должны быть детали ошибки валидации")
        
        (is (= 400 (:status response2)) "Статус ответа должен быть 400")
        (is (false? (:success body2)) "Ответ должен быть неудачным")
        (is (map? (:details body2)) "Должны быть детали ошибки валидации")))))

(deftest test-metrics-endpoint
  (testing "Проверка эндпоинта метрик"
    (with-system [system (system/start (system/new-system test-config))]
      (let [metrics-system (:metrics system)
            _ (metrics/inc-counter! metrics-system ["test" "counter"])
            _ (metrics/record-time! (metrics/timer metrics-system ["test" "timer"]) 100)
            app (routes/app-routes (:db system) (:agent system))
            response (app (request :get "/api/v1/metrics"))
            body (response->data response)]
        
        (is (= 200 (:status response)) "Статус ответа должен быть 200")
        (is (:success body) "Ответ должен быть успешным")
        (is (map? (:data body)) "Данные метрик должны быть картой")
        (is (get-in body [:data :counters "test.counter"]) "Должен быть счетчик test.counter")
        (is (get-in body [:data :timers "test.timer"]) "Должен быть таймер test.timer")))))

;; Запуск тестов при загрузке файла
(comment
  (require '[clojure.test :as test])
  (test/run-tests 'clojure-agent.api.routes-test))
