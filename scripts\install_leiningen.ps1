# Скрипт для установки Leiningen на Windows

# Создаем директорию для Leiningen, если её нет
$leindir = "$env:USERPROFILE\lein"
if (-not (Test-Path -Path $leindir)) {
    New-Item -ItemType Directory -Path $leindir | Out-Null
    Write-Host "Создана директория: $leindir"
}

# Скачиваем lein.bat
$leinUrl = "https://raw.githubusercontent.com/technomancy/leiningen/stable/bin/lein.bat"
$leinBatPath = "$leindir\lein.bat"

Write-Host "Скачивание Leiningen..."
Invoke-WebRequest -Uri $leinUrl -OutFile $leinBatPath

# Добавляем директорию в PATH, если её там ещё нет
$currentPath = [Environment]::GetEnvironmentVariable('Path', 'User')
if ($currentPath -notlike "*$leindir*") {
    [Environment]::SetEnvironmentVariable('Path', "$currentPath;$leindir", 'User')
    Write-Host "Добавлено в PATH: $leindir"
}

# Делаем первый запуск для загрузки зависимостей
Write-Host "Первый запуск Leiningen (может занять некоторое время)..."
Set-Location $leindir
& $leinBatPath version

Write-Host "`nУстановка завершена!`n"
Write-Host "Для использования Leiningen откройте новое окно командной строки и выполните:"
Write-Host "  lein version"
Write-Host "`nИли перезапустите текущее окно PowerShell."
