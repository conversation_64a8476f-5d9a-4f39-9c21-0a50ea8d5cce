{:paths ["src" "resources"]
 :deps {org.clojure/clojure {:mvn/version "1.11.1"}
        ring/ring-core {:mvn/version "1.9.6"}
        ring/ring-jetty-adapter {:mvn/version "1.9.6"}
        ring/ring-json {:mvn/version "0.5.1"}
        ring/ring-defaults {:mvn/version "0.3.4"}
        ring-cors/ring-cors {:mvn/version "0.1.13"}
        metosin/reitit-ring {:mvn/version "0.5.18"}
        metosin/reitit-middleware {:mvn/version "0.5.18"}
        metosin/ring-http-response {:mvn/version "0.9.2"}
        metosin/muuntaja {:mvn/version "0.6.8"}
        com.taoensso/timbre {:mvn/version "5.2.1"}
        com.taoensso/encore {:mvn/version "3.36.0"}
        com.fzakaria/slf4j-timbre {:mvn/version "0.3.21"}
        org.slf4j/slf4j-api {:mvn/version "1.7.36"}
        org.slf4j/slf4j-simple {:mvn/version "1.7.36"}
        com.stuartsierra/component {:mvn/version "1.1.0"}
        com.stuartsierra/component.repl {:mvn/version "0.2.0"}
        org.clojure/core.async {:mvn/version "1.5.648"}
        org.clojure/tools.namespace {:mvn/version "1.3.0"}
        org.clojure/tools.reader {:mvn/version "1.3.6"}
        org.clojure/data.json {:mvn/version "2.4.0"}
        ring/ring-mock {:mvn/version "0.4.0" :scope "test"}
        org.clojure/test.check {:mvn/version "1.1.1" :scope "test"}
        io.aviso/pretty {:mvn/version "1.4.2" :scope "test"}
        expound/expound {:mvn/version "0.9.0" :scope "test"}
        org.clojure/spec.alpha {:mvn/version "0.3.218"}
        org.clojure/core.cache {:mvn/version "1.0.225"}
        org.clojure/core.memoize {:mvn/version "1.0.257"}
        metrics-clojure-ring "2.10.0"
        metrics-clojure-jvm "2.10.0"
        metrics-clojure "2.10.0"
        com.codahale.metrics/metrics-core "4.2.12"
        com.codahale.metrics/metrics-jvm "4.2.12"
        com.codahale.metrics/metrics-json "4.2.12"}
 
 :aliases
 {:test {:extra-paths ["test"]
         :extra-deps {io.github.cognitect-labs/test-runner
                      {:git/url "https://github.com/cognitect-labs/test-runner.git"
                       :sha "b6c319044383973a39900a8e591d8d77c2b31c6e"}}
         :main-opts ["-m" "cognitect.test-runner"]}
  
  :dev {:extra-deps {org.clojure/tools.namespace {:mvn/version "1.3.0"}
                      org.clojure/java.classpath {:mvn/version "1.0.0"}
                      com.bhauman/rebel-readline {:mvn/version "0.1.4"}
                      com.bhauman/figwheel-main {:mvn/version "0.2.16"}}
        :main-opts ["-m" "rebel-readline.main"]}
  
  :repl {:extra-deps {nrepl/nrepl {:mvn/version "1.0.0"}
                       cider/piggieback {:mvn/version "0.5.3"}
                       cider/cider-nrepl {:mvn/version "0.28.5"}}
         :main-opts ["-m" "nrepl.cmdline" "--middleware" "[cider.piggieback/wrap-cljs-repl]"]}}}
