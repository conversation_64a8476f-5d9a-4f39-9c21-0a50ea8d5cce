<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <!-- Имя приложения -->
    <property name="APP_NAME" value="clojure-agent"/>
    
    <!-- Путь к логам -->
    <property name="LOG_PATH" value="./logs"/>
    
    <!-- Формат даты для именования файлов -->
    <timestamp key="byDay" datePattern="yyyyMMdd"/>
    
    <!-- Кодировка -->
    <property name="ENCODING" value="UTF-8"/>
    
    <!-- Шаблон вывода логов -->
    <property name="CONSOLE_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight(%-5level) [%thread] %cyan(%logger{36}): %msg%n"/>
    <property name="FILE_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36}: %msg%n"/>
    
    <!-- Вывод в консоль -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_PATTERN}</pattern>
            <charset>${ENCODING}</charset>
        </encoder>
    </appender>
    
    <!-- Вывод в файл -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${APP_NAME}.log</file>
        <encoder>
            <pattern>${FILE_PATTERN}</pattern>
            <charset>${ENCODING}</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/archives/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- Настройки для различных пакетов -->
    <logger name="org.eclipse.jetty" level="WARN"/>
    <logger name="org.http4s" level="INFO"/>
    <logger name="org.apache.http" level="WARN"/>
    <logger name="com.zaxxer.hikari" level="INFO"/>
    <logger name="org.jooq" level="WARN"/>
    <logger name="org.postgresql" level="WARN"/>
    <logger name="org.h2" level="WARN"/>
    <logger name="com.github.benmanes.caffeine" level="WARN"/>
    <logger name="io.netty" level="WARN"/>
    
    <!-- Корневой логгер -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
    
    <!-- Логи приложения -->
    <logger name="clojure-agent" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </logger>
</configuration>
