(ns clojure-agent.middleware
  "Промежуточное ПО для обработки HTTP-запросов."
  (:require [ring.middleware.defaults :refer [wrap-defaults site-defaults]
             :rename {wrap-defaults wrap-defaults-base}]
            [ring.middleware.json :refer [wrap-json-body wrap-json-response]]
            [ring.middleware.keyword-params :refer [wrap-keyword-params]]
            [ring.middleware.params :refer [wrap-params]]
            [ring.middleware.cors :refer [wrap-cors]]
            [ring.middleware.content-type :refer [wrap-content-type]]
            [ring.middleware.not-modified :refer [wrap-not-modified]]
            [ring.middleware.multipart-params :refer [wrap-multipart-params]]
            [ring.util.response :refer [response status content-type]]
            [taoensso.timbre :as log]
            [cheshire.generate :as json]
            [clojure.string :as str]
            [clojure.walk :as walk]
            [clojure.stacktrace :as st]
            [clj-time.core :as time]
            [clj-time.coerce :as time-coerce]
            [clojure-agent.metrics :as metrics]
            [clojure-agent.config :as config]))

;; ===========================================================================
;; Настройка JSON-сериализации
;; ===========================================================================

;; Настраиваем Cheshire для корректной сериализации ключевых слов и дат
(extend-protocol json/JSONable
  clojure.lang.Keyword
  (to-json [kw gen]
    (json/write-string gen (name kw)))
  
  org.joda.time.DateTime
  (to-json [dt gen]
    (json/write-string gen (time-coerce/to-long dt)))
  
  java.util.UUID
  (to-json [uuid gen]
    (json/write-string gen (str uuid))))

;; ===========================================================================
;; Основные middleware
;; ===========================================================================

(defn wrap-logging
  "Логирование входящих запросов и исходящих ответов."
  [handler]
  (fn [request]
    (let [start-time (System/currentTimeMillis)
          request-id (str (java.util.UUID/randomUUID))
          log-request (assoc request :request-id request-id)
          _ (log/debug "Request:" (select-keys log-request [:request-method :uri :query-params :headers]))]
      (let [response (handler log-request)
            duration (- (System/currentTimeMillis) start-time)]
        (log/debug "Response:" (assoc (select-keys response [:status])
                                     :duration (str duration "ms")))
        (assoc response :duration duration)))))

(defn wrap-exception-handling
  "Обработка исключений и возврат структурированных ошибок."
  [handler]
  (fn [request]
    (try
      (handler request)
      (catch clojure.lang.ExceptionInfo e
        (let [data (ex-data e)]
          (log/error e (str "Ошибка обработки запроса: " (.getMessage e)))
          (-> {:status (or (:status data) 500)
               :body {:error (or (:message e) "Произошла ошибка")
                      :type (or (:type data) :unexpected-error)
                      :details (when-let [details (:details data)] details)}}
              (content-type "application/json"))))
      (catch Exception e
        (log/error e "Необработанное исключение при обработке запроса")
        (-> {:status 500
             :body {:error "Внутренняя ошибка сервера"
                    :type :server-error
                    :details (.getMessage e)}}
            (content-type "application/json"))))))

(defn wrap-authentication
  "Проверка аутентификации пользователя."
  [handler]
  (fn [request]
    (if-let [auth-header (get-in request [:headers "authorization"])]
      (let [[_ token] (re-find #"^Bearer\s+(.*)$" auth-header)]
        (if token
          (try
            ;; TODO: Валидация JWT токена
            (let [claims {:sub "user-id" :roles ["user"]}]
              (handler (assoc request :identity claims)))
            (catch Exception e
              (log/error e "Ошибка при проверке токена аутентификации")
              (-> (response {:error "Неверный токен аутентификации"})
                  (status 401)
                  (content-type "application/json"))))
          (-> (response {:error "Неверный формат заголовка авторизации"})
              (status 401)
              (content-type "application/json"))))
      (-> (response {:error "Требуется аутентификация"})
          (status 401)
          (content-type "application/json")))))

(defn wrap-authorization
  "Проверка прав доступа пользователя."
  [handler required-roles]
  (fn [request]
    (let [user-roles (get-in request [:identity :roles] [])]
      (if (some (set required-roles) user-roles)
        (handler request)
        (-> (response {:error "Недостаточно прав для выполнения операции"})
            (status 403)
            (content-type "application/json"))))))

(defn wrap-metrics
  "Сбор метрик HTTP-запросов."
  [handler metrics-prefix]
  (fn [request]
    (let [start-time (System/nanoTime)
          timer (metrics/timer metrics-prefix ["http" "requests" "duration"])
          counter (metrics/meter metrics-prefix ["http" "requests" "count"])
          _ (metrics/mark-meter! counter)
          response (handler request)
          duration (- (System/nanoTime) start-time)]
      (metrics/time-call timer #(metrics/record-time! timer duration))
      (assoc response :duration duration))))

(defn wrap-content-negotiation
  "Обработка заголовков Accept и Content-Type."
  [handler]
  (fn [request]
    (let [accept (get-in request [:headers "accept"] "application/json")
          content-type (get-in request [:headers "content-type"] "application/json")]
      (-> request
          (assoc-in [:headers "accept"] accept)
          (assoc-in [:headers "content-type"] content-type)
          handler))))

(defn wrap-request-id
  "Добавление уникального идентификатора к каждому запросу."
  [handler]
  (fn [request]
    (let [request-id (or (get-in request [:headers "x-request-id"])
                         (str (java.util.UUID/randomUUID)))]
      (-> request
          (assoc :request-id request-id)
          (update :headers assoc "x-request-id" request-id)
          handler))))

(defn wrap-cors-headers
  "Добавление CORS-заголовков к ответам."
  [handler]
  (fn [request]
    (let [response (handler request)
          origin (get-in request [:headers "origin"] "*")]
      (update-in response [:headers] merge
                 {"Access-Control-Allow-Origin" origin
                  "Access-Control-Allow-Methods" "GET, POST, PUT, DELETE, OPTIONS"
                  "Access-Control-Allow-Headers" "Content-Type, Authorization, X-Requested-With"
                  "Access-Control-Allow-Credentials" "true"}))))

(defn wrap-security-headers
  "Добавление заголовков безопасности."
  [handler]
  (fn [request]
    (let [response (handler request)]
      (update-in response [:headers] merge
                 {"X-Content-Type-Options" "nosniff"
                  "X-Frame-Options" "DENY"
                  "X-XSS-Protection" "1; mode=block"
                  "Content-Security-Policy" (str "default-src 'self'; "
                                                 "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                                                 "style-src 'self' 'unsafe-inline'; "
                                                 "img-src 'self' data:; "
                                                 "font-src 'self' data:;")
                  "Strict-Transport-Security" "max-age=31536000; includeSubDomains"
                  "Referrer-Policy" "strict-origin-when-cross-origin"}))))

(defn wrap-rate-limiting
  "Ограничение частоты запросов."
  [handler rate-limiter]
  (fn [request]
    (if-let [client-ip (or (get-in request [:headers "x-forwarded-for"])
                           (:remote-addr request))]
      (if (rate-limiter client-ip)
        (handler request)
        {:status 429
         :headers {"Content-Type" "application/json"
                   "Retry-After" "60"}
         :body {:error "Слишком много запросов. Пожалуйста, повторите позже."
                :retry-after 60}})
      (handler request))))

;; ===========================================================================
;; Композиция middleware
;; ===========================================================================

defn wrap-defaults
  "Обертка с настройками по умолчанию."
  [handler config]
  (-> handler
      (wrap-defaults-base (assoc-in site-defaults [:security :anti-forgery] false))
      wrap-json-response
      (wrap-json-body {:keywords? true :bigdecimals? true})
      wrap-keyword-params
      wrap-params
      wrap-multipart-params
      wrap-content-type
      wrap-not-modified
      wrap-cors-headers
      wrap-security-headers
      wrap-request-id
      wrap-logging
      wrap-exception-handling
      (wrap-cors :access-control-allow-origin #".*"
                 :access-control-allow-methods [:get :post :put :delete :options]
                 :access-control-allow-headers ["Content-Type" "Authorization"])))

(defn wrap-api
  "Обертка для API-эндпоинтов с аутентификацией."
  [handler]
  (-> handler
      wrap-authentication
      (wrap-authorization ["api"])))

(defn wrap-admin
  "Обертка для административных эндпоинтов."
  [handler]
  (-> handler
      wrap-authentication
      (wrap-authorization ["admin"])))

;; ===========================================================================
;; Утилиты для работы с запросами
;; ===========================================================================

(defn get-query-param
  "Получение параметра запроса с приведением типа."
  ([request key] (get-query-param request key nil))
  ([request key default]
   (let [value (get-in request [:query-params key])]
     (cond
       (nil? value) default
       (and (string? value) (re-matches #"^\d+$" value)) (Long/parseLong value)
       (and (string? value) (re-matches #"^\d+\.\d+$" value)) (Double/parseDouble value)
       (and (string? value) (= "true" (str/lower-case value))) true
       (and (string? value) (= "false" (str/lower-case value))) false
       :else value))))

(defn get-path-param
  "Получение параметра пути с приведением типа."
  ([request key] (get-path-param request key nil))
  ([request key default]
   (let [value (get-in request [:path-params key])]
     (cond
       (nil? value) default
       (and (string? value) (re-matches #"^\d+$" value)) (Long/parseLong value)
       (and (string? value) (= "true" (str/lower-case value))) true
       (and (string? value) (= "false" (str/lower-case value))) false
       :else value))))

(defn get-header
  "Получение значения заголовка с приведением типа."
  ([request header] (get-header request header nil))
  ([request header default]
   (or (get-in request [:headers (str/lower-case header)])
       default)))

(defn json-response
  "Создание JSON-ответа с указанным статусом и телом."
  ([body] (json-response body 200))
  ([body status]
   {:status status
    :headers {"Content-Type" "application/json; charset=utf-8"}
    :body body}))

(defn ok
  "Успешный JSON-ответ."
  ([data] (ok data 200))
  ([data status]
   (json-response {:success true :data data} status)))

(defn error
  "Ошибочный JSON-ответ."
  ([message] (error message 400 nil))
  ([message status] (error message status nil))
  ([message status data]
   (json-response (merge {:success false
                          :error message}
                         (when data {:details data})) status)))

(defn not-found
  "Ответ 404 Not Found."
  ([] (not-found "Ресурс не найден"))
  ([message] (error message 404)))

(defn bad-request
  "Ответ 400 Bad Request."
  ([] (bad-request "Некорректный запрос"))
  ([message] (error message 400)))

(defn unauthorized
  "Ответ 401 Unauthorized."
  ([] (unauthorized "Требуется аутентификация"))
  ([message] (error message 401)))

(defn forbidden
  "Ответ 403 Forbidden."
  ([] (forbidden "Доступ запрещен"))
  ([message] (error message 403)))

(defn internal-server-error
  "Ответ 500 Internal Server Error."
  ([] (internal-server-error "Внутренняя ошибка сервера"))
  ([message] (error message 500)))

(defn service-unavailable
  "Ответ 503 Service Unavailable."
  ([] (service-unavailable "Сервис временно недоступен"))
  ([message] (error message 503)))
