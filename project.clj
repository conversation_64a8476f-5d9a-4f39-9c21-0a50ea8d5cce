(defproject clojure-agent "0.1.0-SNAPSHOT"
  :description "Высоконадежная агентная система на Clojure"
  :url "https://github.com/yourusername/clojure-agent"
  :license {:name "EPL-2.0 OR GPL-2.0-or-later WITH Classpath-exception-2.0"
            :url "https://www.eclipse.org/legal/epl-2.0/"}
  :dependencies [[org.clojure/clojure "1.11.1"]
                 ;; Web
                 [ring/ring-core "1.9.6"]
                 [ring/ring-jetty-adapter "1.9.6"]
                 [ring/ring-json "0.5.1"]
                 [ring-cors "0.1.13"]
                 [metosin/reitit "0.5.18"]
                 [metosin/muuntaja "0.6.8"]
                 [compojure "1.7.0"]
                 
                 ;; Component management
                 [com.stuartsierra/component "1.1.0"]
                 [reloaded.repl "0.2.4"]
                 
                 ;; Async
                 [org.clojure/core.async "1.6.673"]
                 
                 ;; Database
                 [com.github.seancorfield/next.jdbc "1.3.894"]
                 [com.h2database/h2 "2.2.224"]
                 [hikari-cp "3.0.1"]
                 [migratus "1.4.9"]
                 
                 ;; Configuration
                 [aero "1.1.6"]

                 ;; GraphQL
                 [com.walmartlabs/lacinia "1.2.2"]
                 [com.walmartlabs/lacinia-pedestal "1.2"]
                 [io.pedestal/pedestal.service "0.6.0"]
                 [io.pedestal/pedestal.jetty "0.6.0"]

                 ;; Logging
                 [com.taoensso/timbre "6.2.1"]
                 [com.fzakaria/slf4j-timbre "0.3.21"]
                 [org.slf4j/log4j-over-slf4j "2.0.9"]
                 [org.slf4j/jul-to-slf4j "2.0.9"]
                 [org.slf4j/jcl-over-slf4j "2.0.9"]
                 [ch.qos.logback/logback-classic "1.4.11"]]
  
  :main ^:skip-aot clojure-agent.core
  :target-path "target/%s"
  :source-paths ["src"]
  :resource-paths ["resources"]
  :clean-targets [:target-path]
  
  :profiles {:uberjar {:aot :all
                       :jvm-opts ["-Dclojure.compiler.direct-linking=true"]}
             :dev {:dependencies [[org.clojure/tools.namespace "1.3.0"]
                                 [nrepl "1.0.0"]
                                 [cider/piggieback "0.5.3"]
                                 [pjstadig/humane-test-output "0.11.0"]
                                 [eftest "0.6.0"]]
                   :source-paths ["dev"]
                   :resource-paths ["test/resources"]
                   :repl-options {:init-ns user
                                  :nrepl-middleware [cider.piggieback/wrap-cljs-repl]}
                   :injections [(require 'pjstadig.humane-test-output)
                                (pjstadig.humane-test-output/activate!)]}}
             :test {:dependencies [[clj-http "3.12.3"]
                                 [ring/ring-mock "0.4.0"]]}}
  
  :aliases {"test" ["run" "-m" "kaocha.runner" "unit"]
           "test-watch" ["do" ["clean"] ["test"]]}
  
  :test-paths ["test"]
  :jvm-opts ["-Dclojure.compiler.direct-linking=true"]
  :repl-options {:init-ns user
                 :timeout 120000}))
