(ns clojure-agent.db.core
  "Модуль для работы с базой данных"
  (:require [com.stuartsierra.component :as component]
            [next.jdbc :as jdbc]

            [clojure.tools.logging :as log]
            [clojure.string :as str]))

;; Запись пула соединений с БД
(defrecord Database [db-spec]
  component/Lifecycle
  (start [this]
    (log/info "Подключение к базе данных...")
    (try
      (jdbc/execute! db-spec ["SELECT 1"])
      (log/info "Успешное подключение к базе данных")
      this
      (catch Exception e
        (log/error e "Ошибка подключения к базе данных")
        (throw e))))
  (stop [this]
    (log/info "Отключение от базы данных...")
    (when-let [close-fn (:close-fn db-spec)]
      (close-fn))
    (assoc this :db-spec nil)))

;; Функции для работы с агентами в БД
(defn save-agent [db agent-id agent-type state]
  (jdbc/with-transaction [tx (:db-spec db)]
    (jdbc/execute! tx
      ["INSERT INTO agents (id, type, state, created_at, updated_at) VALUES (?, ?, ?, ?, ?)
        ON CONFLICT (id) DO UPDATE SET state = ?, updated_at = ?"
       agent-id agent-type state (java.util.Date.) (java.util.Date.) state (java.util.Date.)])))

(defn load-agent [db agent-id]
  (first
    (jdbc/execute! (:db-spec db)
      ["SELECT * FROM agents WHERE id = ?" agent-id])))

(defn list-agents [db]
  (jdbc/execute! (:db-spec db)
    ["SELECT id, type, created_at, updated_at FROM agents"]))

(defn delete-agent [db agent-id]
  (jdbc/execute! (:db-spec db)
    ["DELETE FROM agents WHERE id = ?" agent-id]))

;; Создание таблиц при инициализации
(defn init-database! [db]
  (jdbc/with-transaction [tx (:db-spec db)]
    (jdbc/execute! tx
      ["CREATE TABLE IF NOT EXISTS agents (
         id VARCHAR(255) PRIMARY KEY,
         type VARCHAR(100) NOT NULL,
         state TEXT NOT NULL,
         created_at TIMESTAMP NOT NULL,
         updated_at TIMESTAMP NOT NULL
       )"])))

;; Фабрика для создания компонента БД
(defn new-db-pool [db-spec]
  (map->Database {:db-spec db-spec}))
