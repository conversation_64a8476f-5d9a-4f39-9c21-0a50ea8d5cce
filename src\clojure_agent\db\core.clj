(ns clojure-agent.db.core
  "Модуль для работы с базой данных"
  (:require [com.stuartsierra.component :as component]
            [clojure.java.jdbc :as jdbc]
            [honey.sql :as sql]
            [honey.sql.helpers :as h]
            [clojure.tools.logging :as log]
            [clojure.string :as str]))

;; Запись пула соединений с БД
(defrecord Database [db-spec]
  component/Lifecycle
  (start [this]
    (log/info "Подключение к базе данных...")
    (try
      (jdbc/query db-spec ["SELECT 1"])
      (log/info "Успешное подключение к базе данных")
      this
      (catch Exception e
        (log/error e "Ошибка подключения к базе данных")
        (throw e))))
  (stop [this]
    (log/info "Отключение от базы данных...")
    (when-let [close-fn (:close-fn db-spec)]
      (close-fn))
    (assoc this :db-spec nil)))

;; Функции для работы с агентами в БД
(defn save-agent [db agent-id agent-type state]
  (jdbc/with-db-transaction [tx (:db-spec db)]
    (jdbc/execute! tx
      (-> (h/insert-into :agents)
          (h/columns :id :type :state :created_at :updated_at)
          (h/values [[agent-id agent-type state (java.util.Date.) (java.util.Date.)]])
          (h/on-conflict [:id]
            (h/do-update-set :state :updated_at)
            (h/where [:= :agents.id agent-id]))
          sql/format))))

(defn load-agent [db agent-id]
  (first
    (jdbc/query (:db-spec db)
      ["SELECT * FROM agents WHERE id = ?" agent-id]
      {:result-set-fn first})))

(defn list-agents [db]
  (jdbc/query (:db-spec db)
    ["SELECT id, type, created_at, updated_at FROM agents"]))

(defn delete-agent [db agent-id]
  (jdbc/execute! (:db-spec db)
    ["DELETE FROM agents WHERE id = ?" agent-id]))

;; Создание таблиц при инициализации
defn init-database! [db]
  (jdbc/with-db-transaction [tx (:db-spec db)]
    (jdbc/execute! tx
      ["CREATE TABLE IF NOT EXISTS agents (
         id VARCHAR(255) PRIMARY KEY,
         type VARCHAR(100) NOT NULL,
         state TEXT NOT NULL,
         created_at TIMESTAMP NOT NULL,
         updated_at TIMESTAMP NOT NULL
       )"])))

;; Фабрика для создания компонента БД
(defn new-db-pool [db-spec]
  (map->Database {:db-spec db-spec}))
