# GraphQL API Документация

## Обзор

Агентная система предоставляет GraphQL API для управления агентами и отправки сообщений. API заменяет предыдущий REST API и предоставляет более гибкий и эффективный способ взаимодействия с системой.

## Эндпоинт

- **URL**: `http://localhost:8080/api`
- **Метод**: `POST`
- **Content-Type**: `application/json`

## GraphQL Playground

В режиме разработки доступен GraphQL Playground по адресу: `http://localhost:8080/ide`

## Схема

### Типы данных

#### Agent
```graphql
type Agent {
  id: String!
  type: String!
  state: JSON
  created_at: String
  updated_at: String
}
```

#### Message
```graphql
type Message {
  type: String!
  payload: JSON
  timestamp: String
}
```

#### MessageResult
```graphql
type MessageResult {
  success: Boolean!
  result: JSON
  error: String
  agent_state: JSON
}
```

#### HealthStatus
```graphql
type HealthStatus {
  status: String!
  timestamp: String!
  uptime: Int
  version: String
}
```

#### Metrics
```graphql
type Metrics {
  agents_count: Int
  messages_processed: Int
  memory_usage: Float
  cpu_usage: Float
}
```

### Input типы

#### CreateAgentInput
```graphql
input CreateAgentInput {
  id: String!
  type: String!
  initial_state: JSON
}
```

#### SendMessageInput
```graphql
input SendMessageInput {
  agent_id: String!
  message_type: String!
  payload: JSON
}
```

## Запросы (Queries)

### health
Проверка состояния системы.

```graphql
query HealthCheck {
  health {
    status
    timestamp
    uptime
    version
  }
}
```

**Пример ответа:**
```json
{
  "data": {
    "health": {
      "status": "ok",
      "timestamp": "2024-01-15T10:30:00Z",
      "uptime": 3600,
      "version": "1.0.0"
    }
  }
}
```

### metrics
Получение метрик системы.

```graphql
query Metrics {
  metrics {
    agents_count
    messages_processed
    memory_usage
    cpu_usage
  }
}
```

**Пример ответа:**
```json
{
  "data": {
    "metrics": {
      "agents_count": 5,
      "messages_processed": 150,
      "memory_usage": 45.2,
      "cpu_usage": 12.8
    }
  }
}
```

### agents
Получение списка всех агентов.

```graphql
query ListAgents {
  agents {
    id
    type
    state
    created_at
    updated_at
  }
}
```

**Пример ответа:**
```json
{
  "data": {
    "agents": [
      {
        "id": "agent-1",
        "type": "worker",
        "state": {"counter": 5, "status": "active"},
        "created_at": "2024-01-15T10:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### agent
Получение конкретного агента по ID.

```graphql
query GetAgent($id: String!) {
  agent(id: $id) {
    id
    type
    state
    created_at
    updated_at
  }
}
```

**Переменные:**
```json
{
  "id": "agent-1"
}
```

**Пример ответа:**
```json
{
  "data": {
    "agent": {
      "id": "agent-1",
      "type": "worker",
      "state": {"counter": 5, "status": "active"},
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

## Мутации (Mutations)

### createAgent
Создание нового агента.

```graphql
mutation CreateAgent($input: CreateAgentInput!) {
  createAgent(input: $input) {
    id
    type
    state
    created_at
    updated_at
  }
}
```

**Переменные:**
```json
{
  "input": {
    "id": "new-agent",
    "type": "worker",
    "initial_state": {
      "counter": 0,
      "status": "initialized"
    }
  }
}
```

**Пример ответа:**
```json
{
  "data": {
    "createAgent": {
      "id": "new-agent",
      "type": "worker",
      "state": {"counter": 0, "status": "initialized"},
      "created_at": "2024-01-15T10:35:00Z",
      "updated_at": "2024-01-15T10:35:00Z"
    }
  }
}
```

### sendMessage
Отправка сообщения агенту.

```graphql
mutation SendMessage($input: SendMessageInput!) {
  sendMessage(input: $input) {
    success
    result
    error
    agent_state
  }
}
```

**Переменные:**
```json
{
  "input": {
    "agent_id": "agent-1",
    "message_type": "update",
    "payload": {
      "counter": 10,
      "message": "Update from GraphQL"
    }
  }
}
```

**Пример ответа:**
```json
{
  "data": {
    "sendMessage": {
      "success": true,
      "result": {"counter": 10, "message": "Update from GraphQL"},
      "error": null,
      "agent_state": {"counter": 10, "message": "Update from GraphQL", "status": "active"}
    }
  }
}
```

## Обработка ошибок

### GraphQL ошибки
Ошибки валидации и выполнения возвращаются в поле `errors`:

```json
{
  "errors": [
    {
      "message": "Агент с указанным ID не найден",
      "locations": [{"line": 2, "column": 3}],
      "path": ["agent"],
      "extensions": {
        "code": "AGENT_NOT_FOUND"
      }
    }
  ],
  "data": {
    "agent": null
  }
}
```

### Бизнес-логика ошибки
Ошибки бизнес-логики возвращаются в данных:

```json
{
  "data": {
    "sendMessage": {
      "success": false,
      "result": null,
      "error": "Агент с ID 'nonexistent' не найден",
      "agent_state": null
    }
  }
}
```

## Примеры использования

### Создание и настройка агента
```graphql
# 1. Создать агента
mutation {
  createAgent(input: {
    id: "data-processor",
    type: "processor",
    initial_state: {
      "processed_count": 0,
      "status": "ready"
    }
  }) {
    id
    type
    state
  }
}

# 2. Отправить сообщение для обработки данных
mutation {
  sendMessage(input: {
    agent_id: "data-processor",
    message_type: "process",
    payload: {
      "data": [1, 2, 3, 4, 5],
      "operation": "sum"
    }
  }) {
    success
    result
    agent_state
  }
}

# 3. Проверить состояние агента
query {
  agent(id: "data-processor") {
    id
    state
  }
}
```

### Мониторинг системы
```graphql
# Получить общую информацию о системе
query SystemStatus {
  health {
    status
    timestamp
    version
  }
  metrics {
    agents_count
    messages_processed
    memory_usage
    cpu_usage
  }
  agents {
    id
    type
    state
  }
}
```

## Интроспекция

GraphQL поддерживает интроспекцию схемы:

```graphql
query IntrospectionQuery {
  __schema {
    queryType { name }
    mutationType { name }
    types {
      name
      kind
      description
      fields {
        name
        type {
          name
          kind
        }
      }
    }
  }
}
```

## Производительность

### Рекомендации
1. Используйте только необходимые поля в запросах
2. Избегайте глубоко вложенных запросов
3. Используйте переменные вместо встроенных значений
4. Кэшируйте результаты на клиенте

### Лимиты
- Максимальная глубина запроса: 10 уровней
- Максимальная сложность запроса: 1000 баллов
- Таймаут запроса: 30 секунд

## Безопасность

### Аутентификация
В продакшене API требует JWT токен в заголовке:
```
Authorization: Bearer <jwt-token>
```

### Авторизация
Различные операции требуют разных ролей:
- `health`, `metrics` - доступно всем
- `agents`, `agent` - требует роль `user`
- `createAgent`, `sendMessage` - требует роль `admin`

## Тестирование

Для тестирования API используйте:

```bash
# Запуск тестового сервера
clojure scripts/test_graphql.clj start-server

# Выполнение всех тестов
clojure scripts/test_graphql.clj test

# Тестирование обработки ошибок
clojure scripts/test_graphql.clj errors
```
