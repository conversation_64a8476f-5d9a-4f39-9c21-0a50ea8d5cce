@echo off
echo Проверка окружения для запуска Clojure-агента...
echo ================================================

:: Проверяем Java
where java >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Ошибка: Java не установлена или не добавлена в PATH.
    echo Пожалуйста, установите JDK 11 или новее:
    echo https://adoptium.net/temurin/releases/
    pause
    exit /b 1
)

echo [OK] Java установлена
java -version

echo.
echo ================================================
echo Установка Clojure CLI...
echo ================================================

:: Проверяем, установлен ли уже Clojure CLI
where clj >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Clojure CLI уже установлен
    clj --version
    goto :run_tests
)

echo Установка Clojure CLI...

:: Создаем временную директорию
set TEMP_DIR=%TEMP%\clojure_install
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"

:: Скачиваем установщик
curl -L -o "%TEMP_DIR%\install.ps1" https://download.clojure.org/install/win-install-1.11.1.1165.ps1
if %ERRORLEVEL% NEQ 0 (
    echo Ошибка при загрузке установщика Clojure CLI
    pause
    exit /b 1
)

:: Запускаем установщик
echo Запуск установщика Clojure CLI...
powershell -ExecutionPolicy Bypass -File "%TEMP_DIR%\install.ps1"
if %ERRORLEVEL% NEQ 0 (
    echo Ошибка при установке Clojure CLI
    pause
    exit /b 1
)

:: Обновляем PATH для текущей сессии
set PATH=%USERPROFILE%\AppData\Local\Programs\clojure-tools;%PATH%
set PATH=%USERPROFILE%\AppData\Local\Programs\clojure-tools\tools\bin;%PATH%

:run_tests
echo.
echo ================================================
echo Запуск тестов...
echo ================================================

call run_tests.bat

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ================================================
    echo Обнаружены ошибки при выполнении тестов!
    echo ================================================
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo ================================================
echo Настройка окружения завершена успешно!
echo ================================================
pause
