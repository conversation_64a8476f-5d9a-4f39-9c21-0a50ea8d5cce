@echo off
echo ================================================
echo Проверка установки Java
echo ================================================

where java >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [УСПЕХ] Java найдена в системе:
    java -version
    echo.
    echo Путь к Java:
    where java
) else (
    echo [ОШИБКА] Java не найдена в системе.
    echo.
    echo Для работы Clojure требуется Java 11 или новее.
    echo Пожалуйста, установите OpenJDK:
    echo 1. Перейдите на https://adoptium.net/
    echo 2. Скачайте и установите "Eclipse Temurin JDK 11" или новее
    echo 3. Убедитесь, что Java добавлена в PATH
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================
echo Проверка версии Java
echo ================================================

for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVAVER=%%g
)

echo Текущая версия Java: %JAVAVER%

rem Проверяем, что версия Java >= 11
for /f "tokens=3 delims=_." %%i in ("%JAVAVER%"") do (
    if "%%i" GEQ "11" (
        echo [УСПЕХ] Версия Java %%i подходит для работы.
    ) else (
        echo [ОШИБКА] Требуется Java 11 или новее. Текущая версия: %%i
        echo Пожалуйста, обновите Java до версии 11 или новее.
        pause
        exit /b 1
    )
)

echo.
echo ================================================
echo Java настроена корректно!
echo ================================================
pause
