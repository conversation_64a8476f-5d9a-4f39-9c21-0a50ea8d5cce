(ns user
  "Пространство имен для интерактивной разработки.
  Автоматически загружается при запуске REPL."
  (:require [clojure.tools.namespace.repl :as tns]
            [clojure.repl :refer :all]
            [clojure.pprint :refer [pprint print-table]]
            [clojure.java.io :as io]
            [clojure.java.jdbc :as jdbc]
            [clojure.stacktrace :as st]
            [clojure.tools.logging :as log]
            [clojure.core.async :as async :refer [<!! >!! <! >! go go-loop chan close!]]
            [taoensso.timbre :as timbre]
            [com.stuartsierra.component :as component]
            [clojure-agent.system :as system]
            [clojure-agent.config :as config]
            [clojure-agent.db.core :as db]
            [clojure-agent.agent.core :as agent]
            [clojure-agent.metrics :as metrics]
            [clojure-agent.cache :as cache]
            [clojure-agent.api.routes :as api]
            [clojure-agent.middleware :as middleware]))

;; ===========================================================================
;; Настройка автоматической перезагрузки кода
;; ===========================================================================

;; Указываем директории для отслеживания изменений
(tns/set-refresh-dirs "dev" "src" "test")

;; ===========================================================================
;; Управление состоянием системы
;; ===========================================================================

(defonce system
  "Текущий запущенный экземпляр системы."
  (atom nil))

(defn init
  "Инициализирует систему с конфигурацией по умолчанию.
  Возвращает новую, но еще не запущенную систему."
  []
  (let [config (config/load-config)]
    (timbre/info "Загрузка конфигурации...")
    (timbre/debug "Конфигурация загружена:" (pr-str config))
    (reset! system (system/new-system config))))

(defn start
  "Запускает текущую систему, если она не запущена.
  Возвращает обновленную систему."
  []
  (when-not @system
    (init))
  (timbre/info "Запуск системы...")
  (let [started (component/start @system)]
    (reset! system started)
    (timbre/info "Система запущена")
    started))

(defn stop
  "Останавливает текущую систему, если она запущена.
  Возвращает остановленную систему."
  []
  (when @system
    (timbre/info "Остановка системы...")
    (let [stopped (component/stop @system)]
      (reset! system stopped)
      (timbre/info "Система остановлена")
      stopped)))

(defn go
  "Инициализирует (если необходимо) и запускает систему.
  Удобная обертка для интерактивной разработки."
  []
  (when-not @system (init))
  (start)
  :ready)

(defn reset
  "Останавливает систему, перезагружает измененный исходный код и перезапускает систему.
  Удобно для инкрементальной разработки."
  []
  (stop)
  (timbre/info "Перезагрузка исходного кода...")
  (let [res (tns/refresh :after 'user/go)]
    (if (instance? Throwable res)
      (do
        (timbre/error "Ошибка при перезагрузке кода:" res)
        (st/print-stack-trace res)
        :error)
      :ok)))

;; ===========================================================================
;; Утилиты для работы с системой
;; ===========================================================================

(defn system-state
  "Возвращает текущее состояние системы в виде карты."
  []
  (when @system
    (into {} (for [[k v] @system] [k (type v)]))))

(defn config
  "Возвращает текущую конфигурацию системы."
  ([] (config nil))
  ([path]
   (let [cfg (some-> @system :config)]
     (if path (get-in cfg path) cfg))))

(defn db-query
  "Выполняет SQL-запрос к базе данных.
  Примеры:
    (db-query "SELECT * FROM agents")
    (db-query ["SELECT * FROM agents WHERE id = ?" 42])"
  [query & [opts]]
  (when-let [db (:db @system)]
    (jdbc/query db (if (vector? query) query [query]) (merge {:identifiers identity} opts))))

(defn clear-cache!
  "Очищает указанный кэш или все кэши, если имя не указано."
  ([]
   (when-let [cache-system (:cache @system)]
     (doseq [[name _] (:caches cache-system)]
       (clear-cache! name))))
  ([cache-name]
   (when-let [cache (get-in @system [:cache :caches (name cache-name)])]
     (cache/clear-cache! cache)
     (timbre/info "Кэш очищен:" (name cache-name)))))

(defn cache-stats
  "Возвращает статистику по кэшам."
  ([]
   (when-let [cache-system (:cache @system)]
     (into {} (for [[name cache] (:caches cache-system)]
                [name (cache/cache-stats cache)]))))
  ([cache-name]
   (when-let [cache (get-in @system [:cache :caches (name cache-name)])]
     (cache/cache-stats cache))))

(defn metrics
  "Возвращает метрики системы."
  []
  (when-let [metrics-system (:metrics @system)]
    (metrics/get-metrics metrics-system)))

;; ===========================================================================
;; Утилиты для работы с агентами
;; ===========================================================================

(defn list-agents
  "Возвращает список всех зарегистрированных агентов."
  []
  (when-let [agent-system (:agent @system)]
    (agent/list-agents agent-system)))

(defn agent-state
  "Возвращает состояние агента по идентификатору."
  [agent-id]
  (when-let [agent-system (:agent @system)]
    (agent/get-agent-state agent-system agent-id)))

(defn send-message
  "Отправляет сообщение агенту.
  Пример: (send-message :my-agent {:type :ping :data "Hello"})"
  [agent-id message]
  (when-let [agent-system (:agent @system)]
    (agent/send-message agent-system agent-id message)))

;; ===========================================================================
;; Профилирование и отладка
;; ===========================================================================

(defmacro time-ms
  "Выполняет выражение и возвращает карту с результатом и временем выполнения."
  [expr]
  `(let [start# (System/currentTimeMillis)
         result# ~expr
         end# (System/currentTimeMillis)]
     {:result result# :time-ms (- end# start#)}))

(defn profile
  "Профилирует выполнение функции.
  Пример: (profile #(some-expensive-function) :samples 1000)"
  [f & {:keys [samples] :or {samples 1}}]
  (let [times (for [_ (range samples)]
                 (let [start (System/nanoTime)
                       result (f)
                       end (System/nanoTime)]
                   {:result result
                    :time-ns (- end start)}))
        total-time (reduce + (map :time-ns times))
        avg-time (/ total-time samples)]
    {:samples samples
     :total-time-ns total-time
     :avg-time-ns avg-time
     :results (map :result times)}))

;; ===========================================================================
;; Примеры использования
;; ===========================================================================
(comment
  ;; Инициализация и запуск системы
  (go)     ; инициализирует и запускает систему
  (reset)  ; перезагружает код и перезапускает систему
  (stop)   ; останавливает систему

  ;; Работа с базой данных
  (db-query "SELECT * FROM sqlite_master")
  (db-query ["SELECT * FROM agents WHERE id = ?" 1])

  ;; Работа с кэшем
  (cache-stats)        ; статистика по всем кэшам
  (cache-stats :users) ; статистика по конкретному кэшу
  (clear-cache!)       ; очистить все кэши
  (clear-cache! :users) ; очистить конкретный кэш

  ;; Работа с агентами
  (list-agents)        ; список всех агентов
  (agent-state :my-agent) ; состояние агента
  (send-message :my-agent {:type :ping :data "Hello"}) ; отправить сообщение агенту

  ;; Мониторинг
  (system-state) ; состояние системы
  (metrics)      ; метрики
  (config)       ; конфигурация
  (config [:http :port]) ; конкретное значение из конфигурации

  ;; Профилирование
  (profile #(do (Thread/sleep 100) :done) :samples 10)
  (time-ms (Thread/sleep 1000))

  ;; Настройка логирования
  (timbre/set-level! :debug)  ; увеличить детализацию логов
  (timbre/set-level! :info)   ; стандартный уровень
  (timbre/set-level! :error)) ; только ошибки
