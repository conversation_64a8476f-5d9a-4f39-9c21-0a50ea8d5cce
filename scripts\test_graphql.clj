#!/usr/bin/env clojure

(ns test-graphql
  "Скрипт для тестирования GraphQL API"
  (:require [clojure.data.json :as json]
            [clj-http.client :as http]
            [clojure.pprint :refer [pprint]]
            [clojure.tools.logging :as log]
            [clojure-agent.system :as system]
            [clojure-agent.config :as config]
            [clojure-agent.graphql.server :as graphql-server]))

;; ===========================================================================
;; Конфигурация для тестирования
;; ===========================================================================

(def test-config
  "Конфигурация для тестирования"
  {:graphql {:host "localhost"
             :port 8080
             :env :test}
   :database {:dbtype "h2"
              :dbname "mem:test"
              :user "sa"
              :password ""}
   :agent {:default-type :worker
           :max-agents 100}
   :metrics {:enabled true}
   :cache {:default-ttl 60
           :max-size 1000}
   :logging {:level :info
             :console? true}})

(def graphql-endpoint "http://localhost:8080/api")

;; ===========================================================================
;; Утилиты для HTTP запросов
;; ===========================================================================

(defn graphql-request
  "Выполняет GraphQL запрос"
  [query & [variables]]
  (try
    (let [body {:query query
                :variables (or variables {})}
          response (http/post graphql-endpoint
                             {:headers {"Content-Type" "application/json"}
                              :body (json/write-str body)
                              :throw-exceptions false})]
      (if (= 200 (:status response))
        (json/read-str (:body response) :key-fn keyword)
        (do
          (println "HTTP Error:" (:status response))
          (println "Body:" (:body response))
          nil)))
    (catch Exception e
      (println "Request failed:" (.getMessage e))
      nil)))

(defn print-result
  "Красиво выводит результат запроса"
  [title result]
  (println (str "\n=== " title " ==="))
  (if result
    (if (:errors result)
      (do
        (println "Ошибки:")
        (pprint (:errors result)))
      (do
        (println "Данные:")
        (pprint (:data result))))
    (println "Нет результата")))

;; ===========================================================================
;; Тестовые запросы
;; ===========================================================================

(def test-queries
  "Набор тестовых запросов"
  {:health
   {:title "Проверка здоровья системы"
    :query "query HealthCheck {
              health {
                status
                timestamp
                uptime
                version
              }
            }"}
   
   :metrics
   {:title "Получение метрик"
    :query "query Metrics {
              metrics {
                agents_count
                messages_processed
                memory_usage
                cpu_usage
              }
            }"}
   
   :list-agents
   {:title "Список агентов"
    :query "query ListAgents {
              agents {
                id
                type
                state
                created_at
                updated_at
              }
            }"}
   
   :create-agent
   {:title "Создание агента"
    :query "mutation CreateAgent($input: CreateAgentInput!) {
              createAgent(input: $input) {
                id
                type
                state
                created_at
                updated_at
              }
            }"
    :variables {:input {:id "test-agent-1"
                       :type "worker"
                       :initial_state {:counter 0
                                      :status "active"}}}}
   
   :get-agent
   {:title "Получение агента"
    :query "query GetAgent($id: String!) {
              agent(id: $id) {
                id
                type
                state
                created_at
                updated_at
              }
            }"
    :variables {:id "test-agent-1"}}
   
   :send-message
   {:title "Отправка сообщения"
    :query "mutation SendMessage($input: SendMessageInput!) {
              sendMessage(input: $input) {
                success
                result
                error
                agent_state
              }
            }"
    :variables {:input {:agent_id "test-agent-1"
                       :message_type "update"
                       :payload {:counter 1
                                :message "Hello from GraphQL!"}}}}
   
   :introspection
   {:title "Интроспекция схемы"
    :query "query IntrospectionQuery {
              __schema {
                queryType { name }
                mutationType { name }
                types {
                  name
                  kind
                  description
                }
              }
            }"}})

;; ===========================================================================
;; Функции тестирования
;; ===========================================================================

(defn run-single-test
  "Выполняет один тест"
  [test-key]
  (let [{:keys [title query variables]} (get test-queries test-key)]
    (println (str "\nВыполнение теста: " title))
    (let [result (graphql-request query variables)]
      (print-result title result)
      result)))

(defn run-all-tests
  "Выполняет все тесты"
  []
  (println "Запуск всех тестов GraphQL API...")
  (doseq [test-key (keys test-queries)]
    (run-single-test test-key)
    (Thread/sleep 1000))) ; Пауза между тестами

(defn test-error-handling
  "Тестирует обработку ошибок"
  []
  (println "\n=== Тестирование обработки ошибок ===")
  
  ;; Тест 1: Несуществующий агент
  (let [result (graphql-request 
                "query GetAgent($id: String!) { agent(id: $id) { id } }"
                {:id "nonexistent-agent"})]
    (print-result "Несуществующий агент" result))
  
  ;; Тест 2: Невалидные данные
  (let [result (graphql-request 
                "mutation CreateAgent($input: CreateAgentInput!) { createAgent(input: $input) { id } }"
                {:input {:id ""  ; Пустой ID
                        :type "worker"}})]
    (print-result "Невалидные данные" result))
  
  ;; Тест 3: Отправка сообщения несуществующему агенту
  (let [result (graphql-request 
                "mutation SendMessage($input: SendMessageInput!) { sendMessage(input: $input) { success error } }"
                {:input {:agent_id "nonexistent"
                        :message_type "test"
                        :payload {}}})]
    (print-result "Сообщение несуществующему агенту" result)))

(defn test-concurrent-operations
  "Тестирует параллельные операции"
  []
  (println "\n=== Тестирование параллельных операций ===")
  (let [futures (doall
                 (for [i (range 5)]
                   (future
                     (graphql-request 
                      "mutation CreateAgent($input: CreateAgentInput!) { createAgent(input: $input) { id } }"
                      {:input {:id (str "concurrent-agent-" i)
                              :type "worker"
                              :initial_state {:index i}}}))))
        results (map deref futures)]
    (doseq [[i result] (map-indexed vector results)]
      (print-result (str "Параллельная операция " (inc i)) result))))

(defn benchmark-queries
  "Бенчмарк производительности запросов"
  []
  (println "\n=== Бенчмарк производительности ===")
  (let [query "query { health { status } }"
        iterations 100
        start-time (System/currentTimeMillis)]
    
    (dotimes [i iterations]
      (graphql-request query)
      (when (zero? (mod i 10))
        (print ".")))
    
    (let [end-time (System/currentTimeMillis)
          duration (- end-time start-time)
          avg-time (/ duration iterations)]
      (println (str "\nВыполнено " iterations " запросов за " duration " мс"))
      (println (str "Среднее время запроса: " avg-time " мс")))))

;; ===========================================================================
;; Главная функция
;; ===========================================================================

(defn wait-for-server
  "Ждет запуска сервера"
  []
  (println "Ожидание запуска GraphQL сервера...")
  (loop [attempts 0]
    (if (< attempts 30)
      (try
        (let [result (graphql-request "query { health { status } }")]
          (if (and result (= "ok" (get-in result [:data :health :status])))
            (println "Сервер запущен!")
            (do
              (Thread/sleep 1000)
              (recur (inc attempts)))))
        (catch Exception e
          (Thread/sleep 1000)
          (recur (inc attempts))))
      (do
        (println "Не удалось дождаться запуска сервера")
        (System/exit 1)))))

(defn -main
  "Главная функция скрипта"
  [& args]
  (let [command (first args)]
    (case command
      "start-server"
      (do
        (println "Запуск тестового сервера...")
        (let [system (system/start (system/new-system test-config))]
          (println "Сервер запущен. Нажмите Ctrl+C для остановки.")
          (.addShutdownHook (Runtime/getRuntime)
                           (Thread. #(system/stop system)))
          @(promise))) ; Ждем бесконечно
      
      "test"
      (do
        (wait-for-server)
        (run-all-tests))
      
      "errors"
      (do
        (wait-for-server)
        (test-error-handling))
      
      "concurrent"
      (do
        (wait-for-server)
        (test-concurrent-operations))
      
      "benchmark"
      (do
        (wait-for-server)
        (benchmark-queries))
      
      "single"
      (let [test-key (keyword (second args))]
        (if (contains? test-queries test-key)
          (do
            (wait-for-server)
            (run-single-test test-key))
          (println "Доступные тесты:" (keys test-queries))))
      
      ;; По умолчанию
      (do
        (println "Использование:")
        (println "  clojure scripts/test_graphql.clj start-server  - Запустить тестовый сервер")
        (println "  clojure scripts/test_graphql.clj test          - Выполнить все тесты")
        (println "  clojure scripts/test_graphql.clj errors        - Тестировать обработку ошибок")
        (println "  clojure scripts/test_graphql.clj concurrent    - Тестировать параллельные операции")
        (println "  clojure scripts/test_graphql.clj benchmark     - Бенчмарк производительности")
        (println "  clojure scripts/test_graphql.clj single <test> - Выполнить один тест")
        (println "")
        (println "Доступные тесты для single:")
        (doseq [test-key (keys test-queries)]
          (println (str "  " test-key " - " (:title (get test-queries test-key))))))))

;; Запуск если файл выполняется напрямую
(when (= *file* (first *command-line-args*))
  (apply -main (rest *command-line-args*)))
